<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="@color/background_color"
    android:padding="32dp"
    android:paddingBottom="48dp"
    android:fitsSystemWindows="true">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="拼图游戏"
        android:textSize="36sp"
        android:textStyle="bold"
        android:textColor="@color/primary_text_color"
        android:layout_marginBottom="48dp" />

    <ImageView
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:src="@drawable/game_logo"
        android:layout_marginBottom="48dp"
        android:scaleType="centerCrop" />

    <!-- 难度选择区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="32dp"
        android:background="@drawable/button_secondary"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🎯 游戏难度"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text_color"
            android:layout_gravity="center"
            android:layout_marginBottom="12dp" />

        <TextView
            android:id="@+id/tv_current_difficulty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="中等 (3×3 格子)"
            android:textSize="16sp"
            android:textColor="@color/secondary_text_color"
            android:layout_gravity="center"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_change_difficulty"
            android:layout_width="160dp"
            android:layout_height="40dp"
            android:text="更改难度"
            android:textSize="14sp"
            android:background="@drawable/button_primary"
            android:textColor="@android:color/white"
            android:layout_gravity="center" />
    </LinearLayout>

    <Button
        android:id="@+id/btn_level_select"
        android:layout_width="200dp"
        android:layout_height="56dp"
        android:text="🎮 选择关卡"
        android:textSize="18sp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/button_primary"
        android:textColor="@android:color/white" />

    <Button
        android:id="@+id/btn_exit"
        android:layout_width="200dp"
        android:layout_height="56dp"
        android:text="退出游戏"
        android:textSize="18sp"
        android:background="@drawable/button_secondary"
        android:textColor="@color/primary_text_color" />

</LinearLayout>
