package com.ai.game_2

import android.content.Context
import android.graphics.*
import android.util.Log
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.cos
import kotlin.math.sin

class CuteImageGenerator(private val context: Context) {

    private val width = 400
    private val height = 400

    // 图片永久存储目录（使用内部存储，不会被清理）
    private val imageDir = File(context.filesDir, "cute_images")

    // 下载失败记录文件
    private val failedDownloadsFile = File(context.filesDir, "failed_downloads.txt")

    // 简笔画搜索关键词
    private val cuteThemes = listOf(
        "cat simple drawing outline",
        "car simple drawing outline",
        "dinosaur simple drawing outline",
        "castle simple drawing outline",
        "rocket simple drawing outline",
        "flower simple drawing outline",
        "robot simple drawing outline",
        "butterfly simple drawing outline",
        "house simple drawing outline",
        "unicorn simple drawing outline"
    )

    init {
        // 创建图片存储目录
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        }
    }

    // 高分辨率简笔画图片源
    private val simpleDrawingImageSources = listOf(
        "https://picsum.photos/800/800?random=",
        "https://picsum.photos/900/900?random=",
        "https://picsum.photos/1000/1000?random=",
        "https://picsum.photos/850/850?random=",
        "https://picsum.photos/950/950?random=",
        "https://picsum.photos/880/880?random=",
        "https://picsum.photos/920/920?random=",
        "https://picsum.photos/980/980?random="
    )

    // 可爱的儿童友好高清图片URL（支持100个关卡）
    private fun getImageUrlsForLevel(level: Int): List<String> {
        // 专门的儿童友好、可爱卡通主题
        val cuteAnimals = listOf(
            "cute+kitten", "adorable+puppy", "kawaii+bunny", "cute+bear", "adorable+panda",
            "kawaii+cat", "cute+dog", "adorable+hamster", "kawaii+fox", "cute+penguin",
            "adorable+koala", "kawaii+elephant", "cute+giraffe", "adorable+lion", "kawaii+tiger",
            "cute+monkey", "adorable+pig", "kawaii+cow", "cute+sheep", "adorable+duck"
        )

        val cuteCharacters = listOf(
            "kawaii+princess", "cute+fairy", "adorable+angel", "kawaii+unicorn", "cute+mermaid",
            "adorable+witch", "kawaii+robot", "cute+alien", "adorable+ghost", "kawaii+monster",
            "cute+superhero", "adorable+pirate", "kawaii+ninja", "cute+wizard", "adorable+knight",
            "kawaii+clown", "cute+chef", "adorable+doctor", "kawaii+teacher", "cute+artist"
        )

        val cuteFood = listOf(
            "kawaii+cake", "cute+cupcake", "adorable+donut", "kawaii+icecream", "cute+candy",
            "adorable+cookie", "kawaii+lollipop", "cute+chocolate", "adorable+fruit", "kawaii+pizza",
            "cute+burger", "adorable+hotdog", "kawaii+sushi", "cute+bread", "adorable+pie",
            "kawaii+juice", "cute+milk", "adorable+tea", "kawaii+coffee", "cute+smoothie"
        )

        val cuteObjects = listOf(
            "kawaii+toy", "cute+doll", "adorable+teddy", "kawaii+ball", "cute+car",
            "adorable+train", "kawaii+plane", "cute+boat", "adorable+rocket", "kawaii+castle",
            "cute+house", "adorable+tree", "kawaii+flower", "cute+star", "adorable+moon",
            "kawaii+sun", "cute+cloud", "adorable+rainbow", "kawaii+heart", "cute+diamond"
        )

        val cuteSeasons = listOf(
            "kawaii+christmas", "cute+halloween", "adorable+easter", "kawaii+birthday", "cute+valentine",
            "adorable+spring", "kawaii+summer", "cute+autumn", "adorable+winter", "kawaii+holiday",
            "cute+party", "adorable+celebration", "kawaii+festival", "cute+carnival", "adorable+picnic",
            "kawaii+beach", "cute+garden", "adorable+park", "kawaii+playground", "cute+school"
        )

        // 合并所有主题
        val allThemes = cuteAnimals + cuteCharacters + cuteFood + cuteObjects + cuteSeasons

        val themeIndex = (level - 1) % allThemes.size
        val theme = allThemes[themeIndex]

        // 专门下载简笔画图片（高分辨率）
        val simpleDrawingKeywords = listOf(
            "简笔画小猫", "简笔画小狗", "简笔画小熊", "简笔画小兔", "简笔画小鸭",
            "简笔画小猪", "简笔画小鸟", "简笔画小鱼", "简笔画小马", "简笔画小羊",
            "简笔画动物", "简笔画宠物", "简笔画卡通", "线条画小猫", "线条画小狗",
            "简笔画玩具", "简笔画娃娃", "简笔画毛绒", "儿童简笔画", "幼儿简笔画",
            "简笔画花朵", "简笔画太阳", "简笔画彩虹", "简笔画云朵", "简笔画星星",
            "简笔画月亮", "简笔画蝴蝶", "简笔画蜜蜂", "简笔画瓢虫", "简笔画蜗牛",
            "简笔画蛋糕", "简笔画冰淇淋", "简笔画糖果", "简笔画水果", "简笔画苹果",
            "简笔画草莓", "简笔画樱桃", "简笔画香蕉", "简笔画橙子", "简笔画葡萄",
            "简笔画房子", "简笔画城堡", "简笔画汽车", "简笔画火车", "简笔画飞机",
            "简笔画轮船", "简笔画气球", "简笔画风车", "简笔画摩天轮", "简笔画滑梯"
        )

        val keyword = simpleDrawingKeywords[(level - 1) % simpleDrawingKeywords.size]

        return listOf(
            // 百度图片搜索 - 简笔画
            "https://image.baidu.com/search/acjson?tn=resultjson_com&logid=&ipn=rj&ct=201326592&is=&fp=result&queryWord=${keyword}&cl=2&lm=-1&ie=utf-8&oe=utf-8&adpicid=&st=-1&z=&ic=0&hd=&latest=&copyright=&word=${keyword}&s=&se=&tab=&width=&height=&face=0&istype=2&qc=&nc=1&fr=&expermode=&force=&pn=0&rn=30&gsm=1e",

            // 搜狗图片搜索 - 简笔画
            "https://pic.sogou.com/pics?query=${keyword}&mode=1&start=0&reqType=ajax&reqFrom=result&tn=0",

            // 360图片搜索 - 简笔画
            "https://image.so.com/j?q=${keyword}&src=srp&correct=${keyword}&sn=0&pn=30&ps=1&cb=realSearchResult",

            // 必应图片搜索 - 简笔画
            "https://www.bing.com/images/search?q=${keyword}&FORM=HDRSC2&first=1&tsc=ImageBasicHover",

            // 备用高质量简笔画图片
            "https://picsum.photos/800/800?random=${level + 1000}",
            "https://picsum.photos/900/900?random=${level + 2000}",
            "https://picsum.photos/1000/1000?random=${level + 3000}",
            "https://picsum.photos/850/850?random=${level + 4000}"
        )
    }
    
    // 检查关卡是否可用（下载是否成功）
    fun isLevelAvailable(level: Int): Boolean {
        // 检查是否有本地图片
        if (hasLocalImage(level)) {
            return true
        }

        // 检查是否在失败列表中
        return !isLevelFailed(level)
    }

    // 异步生成可爱图片
    suspend fun generateCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 检查关卡是否可用
                if (!isLevelAvailable(level)) {
                    return@withContext null // 关卡不可用，返回null
                }

                // 尝试从本地或网络获取图片
                downloadCuteImage(level)
            } catch (e: Exception) {
                // 下载失败，标记关卡为不可用
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 同步版本（保持兼容性）
    fun generateCuteImageSync(level: Int): Bitmap? {
        return runBlocking {
            generateCuteImage(level)
        }
    }
    
    // 从本地或网络获取儿童友好的高清可爱图片
    private suspend fun downloadCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 首先检查本地是否已有图片
                val localBitmap = getLocalImage(level)
                if (localBitmap != null) {
                    return@withContext localBitmap // 直接返回本地图片，不重复下载
                }

                // 本地没有，从网络下载高分辨率简笔画图片
                Log.d("CuteImageGenerator", "开始为关卡 $level 下载高分辨率简笔画图片")
                val imageUrls = getImageUrlsForLevel(level)
                for (imageUrl in imageUrls) {
                    try {
                        Log.d("CuteImageGenerator", "尝试下载高分辨率简笔画: $imageUrl")
                        val bitmap = downloadAndSaveImage(imageUrl, level)
                        if (bitmap != null) {
                            Log.d("CuteImageGenerator", "关卡 $level 高分辨率简笔画下载成功，尺寸: ${bitmap.width}x${bitmap.height}")
                            return@withContext bitmap
                        } else {
                            Log.d("CuteImageGenerator", "高分辨率简笔画下载失败: $imageUrl")
                        }
                    } catch (e: Exception) {
                        Log.e("CuteImageGenerator", "高分辨率简笔画下载异常: $imageUrl", e)
                        continue
                    }
                }

                // 主要源失败，尝试备用高分辨率简笔画图片源
                Log.d("CuteImageGenerator", "主要源失败，尝试备用高分辨率简笔画图片源")
                for ((index, baseUrl) in simpleDrawingImageSources.withIndex()) {
                    try {
                        val backupUrl = "$baseUrl${level + index * 1000}"
                        val bitmap = downloadAndSaveImage(backupUrl, level)
                        if (bitmap != null) {
                            Log.d("CuteImageGenerator", "关卡 $level 备用高分辨率简笔画下载成功")
                            return@withContext bitmap
                        }
                    } catch (e: Exception) {
                        Log.e("CuteImageGenerator", "备用高分辨率简笔画下载异常", e)
                        continue
                    }
                }

                // 所有网络源都失败，标记为失败
                Log.e("CuteImageGenerator", "所有简笔画图片源都失败")
                null
            } catch (e: Exception) {
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 检查本地是否有图片
    private fun hasLocalImage(level: Int): Boolean {
        val imageFile = File(imageDir, "cute_level_$level.png")
        return imageFile.exists()
    }

    // 获取本地图片
    private fun getLocalImage(level: Int): Bitmap? {
        return try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            if (imageFile.exists()) {
                BitmapFactory.decodeFile(imageFile.absolutePath)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    // 下载图片并永久保存
    private suspend fun downloadAndSaveImage(imageUrl: String, level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val bitmap = downloadImageFromUrl(imageUrl)
                if (bitmap != null) {
                    // 永久保存到本地
                    saveBitmapToLocal(bitmap, level)
                    bitmap
                } else {
                    null
                }
            } catch (e: Exception) {
                null
            }
        }
    }

    // 永久保存图片到本地
    private fun saveBitmapToLocal(bitmap: Bitmap, level: Int) {
        try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            val outputStream = FileOutputStream(imageFile)
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream) // 最高质量保存
            outputStream.close()
        } catch (e: Exception) {
            // 保存失败不影响主要功能
        }
    }

    // 检查关卡是否下载失败
    private fun isLevelFailed(level: Int): Boolean {
        return try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",")
                failedLevels.contains(level.toString())
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }

    // 标记关卡为下载失败
    private fun markLevelAsFailed(level: Int) {
        try {
            val failedLevels = if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText().split(",").toMutableSet()
            } else {
                mutableSetOf()
            }

            failedLevels.add(level.toString())
            failedDownloadsFile.writeText(failedLevels.joinToString(","))
        } catch (e: Exception) {
            // 记录失败不影响主要功能
        }
    }

    // 清理所有本地图片和失败记录（重置功能）
    fun clearAllData() {
        try {
            // 删除所有本地图片
            if (imageDir.exists()) {
                imageDir.listFiles()?.forEach { file ->
                    if (file.name.startsWith("cute_level_")) {
                        file.delete()
                    }
                }
            }

            // 删除失败记录
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.delete()
            }
        } catch (e: Exception) {
            // 清理失败不影响主要功能
        }
    }

    // 重新尝试失败的关卡
    fun retryFailedLevel(level: Int) {
        try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",").toMutableSet()
                failedLevels.remove(level.toString())

                if (failedLevels.isEmpty()) {
                    failedDownloadsFile.delete()
                } else {
                    failedDownloadsFile.writeText(failedLevels.joinToString(","))
                }
            }
        } catch (e: Exception) {
            // 重试失败不影响主要功能
        }
    }

    // 检查本地存储大小
    fun getLocalStorageSize(): Long {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()?.sumOf { it.length() } ?: 0L
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }

    // 获取已下载的关卡列表
    fun getDownloadedLevels(): List<Int> {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()
                    ?.filter { it.name.startsWith("cute_level_") && it.name.endsWith(".png") }
                    ?.mapNotNull { file ->
                        val levelStr = file.name.removePrefix("cute_level_").removeSuffix(".png")
                        levelStr.toIntOrNull()
                    }
                    ?.sorted() ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    // 获取失败的关卡列表
    fun getFailedLevels(): List<Int> {
        return try {
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText()
                    .split(",")
                    .mapNotNull { it.toIntOrNull() }
                    .sorted()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    




    // 从URL下载图片
    private suspend fun downloadImageFromUrl(imageUrl: String): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                Log.d("CuteImageGenerator", "开始下载图片: $imageUrl")
                val url = URL(imageUrl)
                val connection = url.openConnection() as HttpURLConnection

                // 设置请求头，模拟浏览器
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                connection.setRequestProperty("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                connection.setRequestProperty("Referer", "https://image.baidu.com/")

                connection.connectTimeout = 15000
                connection.readTimeout = 30000
                connection.doInput = true
                connection.instanceFollowRedirects = true

                val responseCode = connection.responseCode
                Log.d("CuteImageGenerator", "响应码: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val inputStream: InputStream = connection.inputStream
                    val bitmap = BitmapFactory.decodeStream(inputStream)
                    inputStream.close()
                    connection.disconnect()

                    if (bitmap != null) {
                        Log.d("CuteImageGenerator", "图片下载成功，尺寸: ${bitmap.width}x${bitmap.height}")
                    } else {
                        Log.e("CuteImageGenerator", "图片解码失败")
                    }

                    bitmap
                } else {
                    Log.e("CuteImageGenerator", "HTTP错误: $responseCode")
                    connection.disconnect()
                    null
                }
            } catch (e: Exception) {
                Log.e("CuteImageGenerator", "下载图片异常: $imageUrl", e)
                null
            }
        }
    }
    
    // 调整图片大小 - 保持高清质量
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        // 如果原图已经是目标尺寸或更大，保持原图质量
        if (bitmap.width >= targetWidth && bitmap.height >= targetHeight) {
            // 计算缩放比例，保持宽高比
            val scaleX = targetWidth.toFloat() / bitmap.width
            val scaleY = targetHeight.toFloat() / bitmap.height
            val scale = maxOf(scaleX, scaleY) // 使用较大的缩放比例确保填满

            val scaledWidth = (bitmap.width * scale).toInt()
            val scaledHeight = (bitmap.height * scale).toInt()

            // 使用高质量缩放
            val scaledBitmap = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true)

            // 如果缩放后的图片比目标尺寸大，进行居中裁剪
            return if (scaledWidth > targetWidth || scaledHeight > targetHeight) {
                val x = maxOf(0, (scaledWidth - targetWidth) / 2)
                val y = maxOf(0, (scaledHeight - targetHeight) / 2)
                Bitmap.createBitmap(scaledBitmap, x, y,
                    minOf(targetWidth, scaledWidth),
                    minOf(targetHeight, scaledHeight))
            } else {
                scaledBitmap
            }
        } else {
            // 原图太小，直接缩放
            return Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
        }
    }







}


}













}



    private fun drawCuteSun(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 太阳主体 - 金黄色
        paint.color = Color.parseColor("#FFD700")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 100f, paint)

        // 阳光射线 - 橙色
        paint.color = Color.parseColor("#FFA500")
        paint.strokeWidth = 8f
        paint.style = Paint.Style.STROKE
        paint.strokeCap = Paint.Cap.ROUND

        for (i in 0..11) {
            val angle = i * Math.PI / 6
            val startX = centerX + (120f * cos(angle)).toFloat()
            val startY = centerY + (120f * sin(angle)).toFloat()
            val endX = centerX + (160f * cos(angle)).toFloat()
            val endY = centerY + (160f * sin(angle)).toFloat()
            canvas.drawLine(startX, startY, endX, endY, paint)
        }

        // 眼睛 - 黑色
        paint.style = Paint.Style.FILL
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 30f, centerY - 25f, 12f, paint)
        canvas.drawCircle(centerX + 30f, centerY - 25f, 12f, paint)

        // 眼睛高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 25f, centerY - 30f, 4f, paint)
        canvas.drawCircle(centerX + 35f, centerY - 30f, 4f, paint)

        // 腮红 - 粉色
        paint.color = Color.parseColor("#FFB6C1")
        canvas.drawCircle(centerX - 70f, centerY + 10f, 15f, paint)
        canvas.drawCircle(centerX + 70f, centerY + 10f, 15f, paint)

        // 嘴巴 - 红色弧线
        paint.color = Color.parseColor("#FF4500")
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 6f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 40f, centerY + 20f)
        mouthPath.quadTo(centerX, centerY + 50f, centerX + 40f, centerY + 20f)
        canvas.drawPath(mouthPath, paint)
    }

    private fun drawCuteHeart(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 爱心 - 粉色
        paint.color = Color.parseColor("#FF69B4")
        paint.style = Paint.Style.FILL

        val path = Path()
        path.moveTo(centerX, centerY + 80f)
        path.cubicTo(centerX - 80f, centerY - 40f, centerX - 120f, centerY - 120f, centerX - 40f, centerY - 120f)
        path.cubicTo(centerX - 20f, centerY - 140f, centerX + 20f, centerY - 140f, centerX + 40f, centerY - 120f)
        path.cubicTo(centerX + 120f, centerY - 120f, centerX + 80f, centerY - 40f, centerX, centerY + 80f)
        canvas.drawPath(path, paint)

        // 爱心高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 30f, centerY - 60f, 15f, paint)
        canvas.drawCircle(centerX + 30f, centerY - 60f, 10f, paint)
    }

    private fun drawCuteHouse(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 房子主体 - 黄色
        paint.color = Color.parseColor("#FFFF99")
        paint.style = Paint.Style.FILL
        canvas.drawRect(centerX - 80f, centerY, centerX + 80f, centerY + 120f, paint)

        // 屋顶 - 红色
        paint.color = Color.parseColor("#FF6347")
        val roofPath = Path()
        roofPath.moveTo(centerX - 100f, centerY)
        roofPath.lineTo(centerX, centerY - 80f)
        roofPath.lineTo(centerX + 100f, centerY)
        roofPath.close()
        canvas.drawPath(roofPath, paint)

        // 门 - 棕色
        paint.color = Color.parseColor("#8B4513")
        canvas.drawRect(centerX - 20f, centerY + 60f, centerX + 20f, centerY + 120f, paint)

        // 门把手 - 金色
        paint.color = Color.parseColor("#FFD700")
        canvas.drawCircle(centerX + 15f, centerY + 90f, 3f, paint)

        // 窗户 - 蓝色
        paint.color = Color.parseColor("#87CEEB")
        canvas.drawRect(centerX - 60f, centerY + 30f, centerX - 30f, centerY + 60f, paint)
        canvas.drawRect(centerX + 30f, centerY + 30f, centerX + 60f, centerY + 60f, paint)

        // 窗框 - 白色
        paint.color = Color.WHITE
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 3f
        canvas.drawRect(centerX - 60f, centerY + 30f, centerX - 30f, centerY + 60f, paint)
        canvas.drawRect(centerX + 30f, centerY + 30f, centerX + 60f, centerY + 60f, paint)

        // 窗户十字
        canvas.drawLine(centerX - 45f, centerY + 30f, centerX - 45f, centerY + 60f, paint)
        canvas.drawLine(centerX - 60f, centerY + 45f, centerX - 30f, centerY + 45f, paint)
        canvas.drawLine(centerX + 45f, centerY + 30f, centerX + 45f, centerY + 60f, paint)
        canvas.drawLine(centerX + 30f, centerY + 45f, centerX + 60f, centerY + 45f, paint)
    }





    private fun drawCuteCake(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 蛋糕底层 - 粉色
        paint.color = Color.parseColor("#FFB6C1")
        paint.style = Paint.Style.FILL
        canvas.drawRect(centerX - 80f, centerY + 40f, centerX + 80f, centerY + 120f, paint)

        // 蛋糕中层 - 白色
        paint.color = Color.WHITE
        canvas.drawRect(centerX - 60f, centerY - 20f, centerX + 60f, centerY + 40f, paint)

        // 蛋糕顶层 - 黄色
        paint.color = Color.parseColor("#FFFF99")
        canvas.drawRect(centerX - 40f, centerY - 60f, centerX + 40f, centerY - 20f, paint)

        // 蜡烛 - 红色
        paint.color = Color.parseColor("#FF4500")
        canvas.drawRect(centerX - 3f, centerY - 100f, centerX + 3f, centerY - 60f, paint)

        // 火焰 - 橙色
        paint.color = Color.parseColor("#FFA500")
        canvas.drawOval(centerX - 5f, centerY - 110f, centerX + 5f, centerY - 100f, paint)

        // 装饰樱桃 - 红色
        paint.color = Color.parseColor("#DC143C")
        canvas.drawCircle(centerX - 20f, centerY - 40f, 8f, paint)
        canvas.drawCircle(centerX + 20f, centerY - 40f, 8f, paint)
    }

    private fun drawCuteSmile(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 笑脸圆形 - 黄色
        paint.color = Color.parseColor("#FFD700")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 100f, paint)

        // 眼睛 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 35f, centerY - 25f, 15f, paint)
        canvas.drawCircle(centerX + 35f, centerY - 25f, 15f, paint)

        // 眼睛高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 30f, centerY - 30f, 5f, paint)
        canvas.drawCircle(centerX + 40f, centerY - 30f, 5f, paint)

        // 腮红 - 粉色
        paint.color = Color.parseColor("#FFB6C1")
        canvas.drawCircle(centerX - 70f, centerY + 10f, 12f, paint)
        canvas.drawCircle(centerX + 70f, centerY + 10f, 12f, paint)

        // 嘴巴 - 红色
        paint.color = Color.parseColor("#FF4500")
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 6f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 50f, centerY + 20f)
        mouthPath.quadTo(centerX, centerY + 60f, centerX + 50f, centerY + 20f)
        canvas.drawPath(mouthPath, paint)
    }


}
