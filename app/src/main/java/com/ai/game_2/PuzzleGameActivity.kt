package com.ai.game_2

import android.app.AlertDialog
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity

class PuzzleGameActivity : AppCompatActivity() {
    
    private lateinit var puzzleView: PuzzleView
    private lateinit var levelText: TextView
    private lateinit var difficultyText: TextView
    private lateinit var resetButton: Button
    private lateinit var backButton: Button
    
    private var currentLevel: Int = 1
    private lateinit var currentTheme: GameTheme
    private lateinit var gameLevel: GameLevel
    private lateinit var sharedPreferences: SharedPreferences
    private var customPhotoPath: String? = null
    private var customGridCols: Int = 0
    private var customGridRows: Int = 0
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_puzzle_game)

        sharedPreferences = getSharedPreferences("PuzzleGame", MODE_PRIVATE)
        currentLevel = intent.getIntExtra("level", 1)

        // 获取主题参数
        val themeName = intent.getStringExtra("theme") ?: GameTheme.CUTE.name
        currentTheme = GameTheme.valueOf(themeName)

        // 获取自定义照片路径和网格参数（如果是拍照主题）
        customPhotoPath = intent.getStringExtra("custom_photo_path")
        customGridCols = intent.getIntExtra("custom_grid_cols", 0)
        customGridRows = intent.getIntExtra("custom_grid_rows", 0)

        initializeViews()
        setupGame()
        setupClickListeners()
    }
    
    private fun initializeViews() {
        puzzleView = findViewById(R.id.puzzle_view)
        levelText = findViewById(R.id.tv_level)
        difficultyText = findViewById(R.id.tv_difficulty)
        resetButton = findViewById(R.id.btn_reset)
        backButton = findViewById(R.id.btn_back)
    }
    
    private fun setupGame() {
        val levels = GameLevelManager.getLevelsForTheme(currentTheme)
        gameLevel = levels.find { it.levelNumber == currentLevel }
            ?: levels.first()
        
        levelText.text = "第 ${gameLevel.levelNumber} 关"

        // 根据主题类型显示拼图块数量
        val pieceCount = if (currentTheme == GameTheme.CUSTOM_PHOTO && customGridCols > 0 && customGridRows > 0) {
            "${customGridCols}×${customGridRows} (${customGridCols * customGridRows}块)"
        } else if (currentTheme == GameTheme.CUSTOM_PHOTO) {
            "智能分配"  // 拍照主题显示智能分配
        } else {
            "9 块"  // 其他主题固定9块
        }
        difficultyText.text = "${gameLevel.title} ($pieceCount)"

        // 根据主题类型初始化拼图
        when (currentTheme) {
            GameTheme.CUSTOM_PHOTO -> {
                if (customPhotoPath != null) {
                    if (customGridCols > 0 && customGridRows > 0) {
                        // 使用用户自定义的网格
                        puzzleView.initializePuzzleWithCustomPhotoAndGrid(customPhotoPath!!, customGridCols, customGridRows)
                    } else {
                        // 使用智能网格
                        puzzleView.initializePuzzleWithCustomPhoto(customPhotoPath!!)
                    }
                }
            }
            GameTheme.CUTE -> {
                // 可爱主题使用程序生成的图片
                puzzleView.initializePuzzleWithGeneratedImage(currentLevel)
            }
            else -> {
                // 其他主题使用资源图片
                puzzleView.initializePuzzle(gameLevel.imageResourceId, gameLevel.gridSize)
            }
        }
        
        puzzleView.onPuzzleCompleted = {
            Log.d("PuzzleGameActivity", "收到拼图完成回调")
            onPuzzleCompleted()
        }
    }
    
    private fun setupClickListeners() {
        resetButton.setOnClickListener {
            showResetConfirmDialog()
        }
        
        backButton.setOnClickListener {
            finish()
        }
    }
    
    private fun showResetConfirmDialog() {
        AlertDialog.Builder(this)
            .setTitle("重置拼图")
            .setMessage("确定要重新开始这一关吗？")
            .setPositiveButton("确定") { _, _ ->
                puzzleView.resetPuzzle()
                Toast.makeText(this, "拼图已重置", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun onPuzzleCompleted() {
        Log.d("PuzzleGameActivity", "拼图完成处理开始")
        // 保存关卡进度
        saveProgress()

        // 显示完成对话框
        showCompletionDialog()
    }
    
    private fun saveProgress() {
        val editor = sharedPreferences.edit()
        val maxUnlockedLevelKey = "max_unlocked_level_${currentTheme.name}"
        val maxUnlockedLevel = sharedPreferences.getInt(maxUnlockedLevelKey, 1)

        if (currentLevel >= maxUnlockedLevel) {
            editor.putInt(maxUnlockedLevelKey, currentLevel + 1)
        }

        editor.putBoolean("level_${currentTheme.name}_${currentLevel}_completed", true)
        editor.apply()
    }
    
    private fun showCompletionDialog() {
        showCongratulationsAnimation()
    }

    private fun showCongratulationsAnimation() {
        // 创建恭喜动画覆盖层
        val inflater = LayoutInflater.from(this)
        val congratulationsView = inflater.inflate(R.layout.congratulations_overlay, null)

        // 添加到根布局
        val rootLayout = findViewById<FrameLayout>(android.R.id.content)
        rootLayout.addView(congratulationsView)

        // 获取视图元素
        val container = congratulationsView.findViewById<LinearLayout>(R.id.congratulationsContainer)
        val levelText = congratulationsView.findViewById<TextView>(R.id.levelCompletedText)
        val characterText = congratulationsView.findViewById<TextView>(R.id.characterNameText)
        val encouragementText = congratulationsView.findViewById<TextView>(R.id.encouragementText)
        val backButton = congratulationsView.findViewById<Button>(R.id.backToMenuButton)
        val nextButton = congratulationsView.findViewById<Button>(R.id.nextLevelButton)
        val closeButton = congratulationsView.findViewById<Button>(R.id.closeButton)

        // 设置文本内容
        levelText.text = "第 $currentLevel 关完成！"
        characterText.text = gameLevel.title

        // 随机鼓励文字
        val encouragements = arrayOf(
            "太棒了！你真厉害！ 💖",
            "哇！你好聪明！ ✨",
            "完美！继续加油！ 🌟",
            "真是太棒了！ 🎉",
            "你是拼图小天才！ 👑"
        )
        encouragementText.text = encouragements.random()

        // 检查是否是最后一关
        val totalLevels = when (currentTheme) {
            GameTheme.CUSTOM_PHOTO -> {
                // 拍照主题：基于照片数量
                intent.getStringArrayListExtra("photo_paths")?.size ?: 1
            }
            GameTheme.VOICE_SEARCH -> {
                // 语音搜图主题：基于搜索结果数量
                intent.getStringArrayListExtra("image_urls")?.size ?: 1
            }
            else -> {
                // 其他主题：使用默认关卡数量
                GameLevelManager.getLevelsForTheme(currentTheme).size
            }
        }

        val isLastLevel = currentLevel >= totalLevels
        if (isLastLevel) {
            nextButton.text = "🏆 完成游戏"
            nextButton.setOnClickListener {
                showGameCompletedDialog()
                rootLayout.removeView(congratulationsView)
            }
        } else {
            nextButton.setOnClickListener {
                rootLayout.removeView(congratulationsView)
                startNextLevel()
            }
        }

        backButton.setOnClickListener {
            rootLayout.removeView(congratulationsView)
            finish()
        }

        closeButton.setOnClickListener {
            rootLayout.removeView(congratulationsView)
        }

        // 开始动画
        startCongratulationsAnimations(congratulationsView, container)
    }

    private fun startCongratulationsAnimations(overlayView: View, container: LinearLayout) {
        // 容器缩放动画
        val scaleAnimation = AnimationUtils.loadAnimation(this, R.anim.congratulations_scale_in)
        container.startAnimation(scaleAnimation)

        // 星星闪烁动画
        val starsContainer = overlayView.findViewById<LinearLayout>(R.id.starsContainer)
        val star1 = starsContainer.findViewById<TextView>(R.id.star1)
        val star2 = starsContainer.findViewById<TextView>(R.id.star2)
        val star3 = starsContainer.findViewById<TextView>(R.id.star3)

        val twinkleAnimation = AnimationUtils.loadAnimation(this, R.anim.star_twinkle)

        // 延迟启动星星动画
        Handler(Looper.getMainLooper()).postDelayed({
            star1.startAnimation(twinkleAnimation)
        }, 500)

        Handler(Looper.getMainLooper()).postDelayed({
            star2.startAnimation(twinkleAnimation)
        }, 700)

        Handler(Looper.getMainLooper()).postDelayed({
            star3.startAnimation(twinkleAnimation)
        }, 900)

        // 飘落星星动画
        startFallingStarsAnimation(overlayView)
    }

    private fun startFallingStarsAnimation(overlayView: View) {
        val fallingStars = listOf(
            overlayView.findViewById<ImageView>(R.id.fallingStars1),
            overlayView.findViewById<ImageView>(R.id.fallingStars2),
            overlayView.findViewById<ImageView>(R.id.fallingStars3),
            overlayView.findViewById<ImageView>(R.id.fallingStars4)
        )

        val fallAnimation = AnimationUtils.loadAnimation(this, R.anim.star_fall)

        fallingStars.forEachIndexed { index, star ->
            Handler(Looper.getMainLooper()).postDelayed({
                star.visibility = View.VISIBLE
                star.startAnimation(fallAnimation)
            }, (index * 300).toLong())
        }
    }

    private fun showGameCompletedDialog() {
        AlertDialog.Builder(this)
            .setTitle("🎉 游戏完成！")
            .setMessage("恭喜你完成了所有关卡！\n你真是太厉害了！ 👑")
            .setPositiveButton("返回主菜单") { _, _ ->
                finish()
            }
            .setCancelable(false)
            .show()
    }
    
    private fun startNextLevel() {
        val nextLevelIntent = Intent(this, PuzzleGameActivity::class.java)
        nextLevelIntent.putExtra("theme", currentTheme.name)
        nextLevelIntent.putExtra("level", currentLevel + 1)

        // 传递拍照主题的额外参数
        if (currentTheme == GameTheme.CUSTOM_PHOTO) {
            val photoPath = intent.getStringExtra("custom_photo_path")
            val gridCols = intent.getIntExtra("custom_grid_cols", 3)
            val photoPaths = intent.getStringArrayListExtra("photo_paths")

            if (photoPaths != null && currentLevel < photoPaths.size) {
                val nextPhotoPath = photoPaths[currentLevel] // currentLevel是下一关的索引(从0开始)
                nextLevelIntent.putExtra("custom_photo_path", nextPhotoPath)
                nextLevelIntent.putExtra("custom_grid_cols", gridCols)
                nextLevelIntent.putExtra("useDynamicGrid", true)
                nextLevelIntent.putStringArrayListExtra("photo_paths", photoPaths)
            }
        }

        // 传递语音搜图主题的额外参数
        if (currentTheme == GameTheme.VOICE_SEARCH) {
            val searchQuery = intent.getStringExtra("search_query")
            val imageUrls = intent.getStringArrayListExtra("image_urls")
            val useDynamicGrid = intent.getBooleanExtra("useDynamicGrid", false)

            nextLevelIntent.putExtra("search_query", searchQuery)
            nextLevelIntent.putStringArrayListExtra("image_urls", imageUrls)
            nextLevelIntent.putExtra("useDynamicGrid", useDynamicGrid)
        }

        startActivity(nextLevelIntent)
        finish()
    }
}
