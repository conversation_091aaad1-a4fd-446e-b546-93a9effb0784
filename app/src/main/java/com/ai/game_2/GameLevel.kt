package com.ai.game_2

data class GameLevel(
    val levelNumber: Int,
    val gridSize: Int,
    val imageResourceId: Int,
    val title: String,
    val theme: GameTheme,
    val isUnlocked: Boolean = false,
    val imageUrl: String? = null // 支持网络图片URL
)

enum class GameTheme(val displayName: String, val emoji: String) {
    CUTE("可爱主题", "🥰"),
    CUSTOM_PHOTO("拍照拼图", "📸"),
    VOICE_SEARCH("语音搜图", "🎤")
}

object GameLevelManager {
    fun getAllThemes(): List<GameTheme> {
        return GameTheme.values().toList()
    }

    fun getLevelsForTheme(theme: GameTheme): List<GameLevel> {
        return when (theme) {
            GameTheme.CUTE -> getCuteLevels()
            GameTheme.CUSTOM_PHOTO -> getCustomPhotoLevels()
            GameTheme.VOICE_SEARCH -> getVoiceSearchLevels()
        }
    }

    private fun getCuteLevels(): List<GameLevel> {
        return listOf(
            GameLevel(1, 3, 0, "可爱小猫 🐱", GameTheme.CUTE, true),
            GameLevel(2, 3, 0, "萌萌小狗 🐶", GameTheme.CUTE, false),
            GameLevel(3, 3, 0, "彩虹独角兽 🦄", GameTheme.CUTE, false),
            GameLevel(4, 3, 0, "粉色小兔 🐰", GameTheme.CUTE, false),
            GameLevel(5, 3, 0, "甜甜花朵 🌸", GameTheme.CUTE, false),
            GameLevel(6, 3, 0, "星星月亮 ⭐", GameTheme.CUTE, false),
            GameLevel(7, 3, 0, "爱心气球 💖", GameTheme.CUTE, false),
            GameLevel(8, 3, 0, "蝴蝶花园 🦋", GameTheme.CUTE, false),
            GameLevel(9, 3, 0, "彩虹糖果 🍭", GameTheme.CUTE, false),
            GameLevel(10, 3, 0, "梦幻城堡 🏰", GameTheme.CUTE, false)
        )
    }

    private fun getCustomPhotoLevels(): List<GameLevel> {
        return listOf(
            GameLevel(1, 3, R.drawable.ic_camera_placeholder, "拍照拼图 📸", GameTheme.CUSTOM_PHOTO, true)
        )
    }

    private fun getVoiceSearchLevels(): List<GameLevel> {
        return listOf(
            GameLevel(1, 3, R.drawable.ic_voice_placeholder, "语音搜图 1 🎤", GameTheme.VOICE_SEARCH, true),
            GameLevel(2, 3, R.drawable.ic_voice_placeholder, "语音搜图 2 🔍", GameTheme.VOICE_SEARCH, false),
            GameLevel(3, 3, R.drawable.ic_voice_placeholder, "语音搜图 3 🖼️", GameTheme.VOICE_SEARCH, false),
            GameLevel(4, 3, R.drawable.ic_voice_placeholder, "语音搜图 4 🌟", GameTheme.VOICE_SEARCH, false),
            GameLevel(5, 3, R.drawable.ic_voice_placeholder, "语音搜图 5 🎨", GameTheme.VOICE_SEARCH, false),
            GameLevel(6, 3, R.drawable.ic_voice_placeholder, "语音搜图 6 🌈", GameTheme.VOICE_SEARCH, false),
            GameLevel(7, 3, R.drawable.ic_voice_placeholder, "语音搜图 7 ⭐", GameTheme.VOICE_SEARCH, false),
            GameLevel(8, 3, R.drawable.ic_voice_placeholder, "语音搜图 8 🎯", GameTheme.VOICE_SEARCH, false),
            GameLevel(9, 3, R.drawable.ic_voice_placeholder, "语音搜图 9 🚀", GameTheme.VOICE_SEARCH, false),
            GameLevel(10, 3, R.drawable.ic_voice_placeholder, "语音搜图 10 🏆", GameTheme.VOICE_SEARCH, false)
        )
    }

    fun getDifficultyText(gridSize: Int): String {
        return when (gridSize) {
            3 -> "超简单 ⭐"
            4 -> "有点难 ⭐⭐"
            5 -> "好难哦 ⭐⭐⭐"
            6 -> "超级难 ⭐⭐⭐⭐"
            7 -> "最难的 ⭐⭐⭐⭐⭐"
            else -> "特别的"
        }
    }

    fun getPieceCount(gridSize: Int): Int {
        return gridSize * gridSize
    }
}
