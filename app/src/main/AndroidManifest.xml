<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Game_2"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".PuzzleGameActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".LevelSelectActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ThemeSelectActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".CameraActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".VoiceSearchActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".VoicePuzzleActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".TestActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.ai.game_2.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>