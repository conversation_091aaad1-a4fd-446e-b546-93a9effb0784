package com.ai.game_2

import android.graphics.Bitmap
import android.graphics.RectF

data class PuzzlePiece(
    val id: Int,
    val correctRow: Int,
    val correctCol: Int,
    var currentRow: Int,
    var currentCol: Int,
    val bitmap: Bitmap,
    val bounds: RectF = RectF()
) {
    fun isInCorrectPosition(): Boolean {
        return correctRow == currentRow && correctCol == currentCol
    }
    
    fun swapPosition(other: PuzzlePiece) {
        val tempRow = this.currentRow
        val tempCol = this.currentCol
        
        this.currentRow = other.currentRow
        this.currentCol = other.currentCol
        
        other.currentRow = tempRow
        other.currentCol = tempCol
    }
    
    fun updateBounds(left: Float, top: Float, right: Float, bottom: Float) {
        bounds.set(left, top, right, bottom)
    }
    
    fun contains(x: Float, y: Float): Boolean {
        return bounds.contains(x, y)
    }
}
