# Download princess images

Write-Host "Downloading princess images..." -ForegroundColor Green

# Create directory
$drawableDir = "app\src\main\res\drawable"
if (!(Test-Path $drawableDir)) {
    New-Item -ItemType Directory -Path $drawableDir -Force
}

# Sample images - these are placeholder URLs, you'll need real ones
$images = @(
    @{
        url = "https://via.placeholder.com/512x512/FFB6C1/000000?text=Princess1"
        filename = "puzzle_image_1.png"
    },
    @{
        url = "https://via.placeholder.com/512x512/87CEEB/000000?text=Mermaid"
        filename = "puzzle_image_2.png"
    },
    @{
        url = "https://via.placeholder.com/512x512/FFE4E1/000000?text=Unicorn"
        filename = "puzzle_image_3.png"
    },
    @{
        url = "https://via.placeholder.com/512x512/F0FFF0/000000?text=Fairy"
        filename = "puzzle_image_4.png"
    },
    @{
        url = "https://via.placeholder.com/512x512/DDA0DD/000000?text=Pony"
        filename = "puzzle_image_5.png"
    }
)

foreach ($img in $images) {
    try {
        Write-Host "Downloading $($img.filename)..." -ForegroundColor Yellow
        $filepath = Join-Path $drawableDir $img.filename
        Invoke-WebRequest -Uri $img.url -OutFile $filepath -ErrorAction Stop
        Write-Host "Success: $($img.filename)" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed: $($img.filename)" -ForegroundColor Red
    }
}

Write-Host "Download complete!" -ForegroundColor Green
