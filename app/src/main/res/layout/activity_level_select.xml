<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color"
    android:fitsSystemWindows="true"
    android:paddingBottom="16dp">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/header_background"
        android:gravity="center_vertical">

        <Button
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/ic_back"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="选择关卡"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text_color"
            android:gravity="center" />

        <!-- 占位符，保持标题居中 -->
        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />

    </LinearLayout>

    <!-- 关卡网格 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_levels"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp"
        android:clipToPadding="false" />

</LinearLayout>
