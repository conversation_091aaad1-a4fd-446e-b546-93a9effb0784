package com.ai.game_2

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.*

class ThemeAdapter(
    private val themes: List<GameTheme>,
    private val onThemeClick: (GameTheme) -> Unit
) : RecyclerView.Adapter<ThemeAdapter.ThemeViewHolder>() {
    
    class ThemeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val themeImage: ImageView = itemView.findViewById(R.id.iv_theme_image)
        val themeName: TextView = itemView.findViewById(R.id.tv_theme_name)
        val themeDescription: TextView = itemView.findViewById(R.id.tv_theme_description)
        val levelCount: TextView = itemView.findViewById(R.id.tv_level_count)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ThemeViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_theme, parent, false)
        return ThemeViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ThemeViewHolder, position: Int) {
        val theme = themes[position]
        
        holder.themeName.text = "${theme.displayName} ${theme.emoji}"
        val levelCount = GameLevelManager.getLevelsForTheme(theme).size
        holder.levelCount.text = "${levelCount}个关卡"
        
        when (theme) {
            GameTheme.CUTE -> {
                // 先显示占位符，然后异步加载可爱图片
                holder.themeImage.setImageResource(R.drawable.ic_voice_placeholder)
                holder.themeDescription.text = "超可爱的卡通角色，让拼图变得更有趣！"

                // 异步生成图片，避免阻塞主线程
                CoroutineScope(Dispatchers.Main).launch {
                    try {
                        val generator = CuteImageGenerator()
                        val cuteBitmap = generator.generateCuteImage(1, 200, 200, holder.itemView.context.cacheDir) // 异步生成第1关的可爱小猫
                        if (cuteBitmap != null) {
                            holder.themeImage.setImageBitmap(cuteBitmap)
                        } else {
                            // 如果关卡不可用，保持占位符
                            holder.themeImage.setImageResource(R.drawable.ic_voice_placeholder)
                        }
                    } catch (e: Exception) {
                        // 如果生成失败，保持占位符
                        holder.themeImage.setImageResource(R.drawable.ic_voice_placeholder)
                    }
                }
            }
            GameTheme.CUSTOM_PHOTO -> {
                holder.themeImage.setImageResource(R.drawable.ic_camera_placeholder)
                holder.themeDescription.text = "拍照制作专属拼图，发挥无限创意！"
            }
            GameTheme.VOICE_SEARCH -> {
                holder.themeImage.setImageResource(R.drawable.ic_voice_placeholder)
                holder.themeDescription.text = "语音搜索网络图片，创造无限可能！"
            }
        }
        
        holder.itemView.setOnClickListener {
            onThemeClick(theme)
        }
    }
    
    override fun getItemCount(): Int = themes.size
}
