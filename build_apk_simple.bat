@echo off
echo Building Princess Puzzle Game APK...
echo.

REM Get current date
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "datestamp=%YYYY%%MM%%DD%"

echo Build date: %datestamp%
echo.

REM Check image files
echo Checking puzzle images...
if not exist "app\src\main\res\drawable\puzzle_image_1.png" (
    echo ERROR: puzzle_image_1.png missing
    pause
    exit /b 1
)
if not exist "app\src\main\res\drawable\puzzle_image_2.png" (
    echo ERROR: puzzle_image_2.png missing
    pause
    exit /b 1
)
if not exist "app\src\main\res\drawable\puzzle_image_3.png" (
    echo ERROR: puzzle_image_3.png missing
    pause
    exit /b 1
)
if not exist "app\src\main\res\drawable\puzzle_image_4.png" (
    echo ERROR: puzzle_image_4.png missing
    pause
    exit /b 1
)
if not exist "app\src\main\res\drawable\puzzle_image_5.png" (
    echo ERROR: puzzle_image_5.png missing
    pause
    exit /b 1
)

echo All images found!
echo.

REM Clean previous build
echo Cleaning previous build...
call gradlew.bat clean

echo.
echo Building APK...
call gradlew.bat assembleDebug
if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo APK build successful!

REM Create release directory
if not exist "release" mkdir release

REM Copy and rename APK
set "apk_path=app\build\outputs\apk\debug\app-debug.apk"
set "new_apk_name=%datestamp%_PrincessPuzzle_debug.apk"
set "output_path=release\%new_apk_name%"

copy "%apk_path%" "%output_path%"

echo.
echo APK created: %new_apk_name%
echo Location: %output_path%
echo.

echo Build complete!
pause
