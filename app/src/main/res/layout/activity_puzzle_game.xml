<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    android:fitsSystemWindows="true">

    <!-- 主游戏内容 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingBottom="16dp">

    <!-- 顶部信息栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/header_background"
        android:gravity="center_vertical">

        <Button
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/ic_back"
            android:layout_marginEnd="16dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_level"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="第 1 关"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/primary_text_color" />

            <TextView
                android:id="@+id/tv_difficulty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="简单 3x3 (9 块)"
                android:textSize="14sp"
                android:textColor="@color/secondary_text_color" />

        </LinearLayout>

        <!-- 参考图 -->
        <androidx.cardview.widget.CardView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            xmlns:app="http://schemas.android.com/apk/res-auto">

            <ImageView
                android:id="@+id/iv_reference"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/light_gray"
                android:contentDescription="参考图" />

        </androidx.cardview.widget.CardView>

        <Button
            android:id="@+id/btn_reset"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:text="重置"
            android:textSize="14sp"
            android:background="@drawable/button_secondary"
            android:textColor="@color/primary_text_color"
            android:paddingHorizontal="16dp" />

    </LinearLayout>

    <!-- 拼图游戏区域 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <com.ai.game_2.PuzzleView
            android:id="@+id/puzzle_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/puzzle_background" />

    </FrameLayout>

    <!-- 底部提示信息 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="拖拽拼图块到正确位置完成拼图"
        android:textSize="14sp"
        android:textColor="@color/secondary_text_color"
        android:gravity="center"
        android:padding="16dp"
        android:background="@color/footer_background" />

    </LinearLayout>

</FrameLayout>
