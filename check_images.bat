@echo off
echo 🧽 检查海绵宝宝拼图图片文件状态...
echo.

set "drawable_path=app\src\main\res\drawable"

echo 📁 检查目录: %drawable_path%
echo.

REM 检查每个图片文件
echo 🔍 检查图片文件:
echo.

if exist "%drawable_path%\puzzle_image_1.jpg" (
    echo ✅ puzzle_image_1.jpg 已找到 ^(海绵宝宝^)
) else if exist "%drawable_path%\puzzle_image_1.png" (
    echo ✅ puzzle_image_1.png 已找到 ^(海绵宝宝^)
) else (
    echo ❌ puzzle_image_1 未找到 ^(海绵宝宝^)
    echo    请下载: https://pixabay.com/illustrations/search/spongebob%%20squarepants/
)

if exist "%drawable_path%\puzzle_image_2.jpg" (
    echo ✅ puzzle_image_2.jpg 已找到 ^(派大星^)
) else if exist "%drawable_path%\puzzle_image_2.png" (
    echo ✅ puzzle_image_2.png 已找到 ^(派大星^)
) else (
    echo ❌ puzzle_image_2 未找到 ^(派大星^)
    echo    请下载: https://pixabay.com/illustrations/search/patrick%%20star%%20spongebob/
)

if exist "%drawable_path%\puzzle_image_3.jpg" (
    echo ✅ puzzle_image_3.jpg 已找到 ^(章鱼哥^)
) else if exist "%drawable_path%\puzzle_image_3.png" (
    echo ✅ puzzle_image_3.png 已找到 ^(章鱼哥^)
) else (
    echo ❌ puzzle_image_3 未找到 ^(章鱼哥^)
    echo    请下载: https://pixabay.com/illustrations/search/squidward%%20tentacles/
)

if exist "%drawable_path%\puzzle_image_4.jpg" (
    echo ✅ puzzle_image_4.jpg 已找到 ^(蟹老板^)
) else if exist "%drawable_path%\puzzle_image_4.png" (
    echo ✅ puzzle_image_4.png 已找到 ^(蟹老板^)
) else (
    echo ❌ puzzle_image_4 未找到 ^(蟹老板^)
    echo    请下载: https://pixabay.com/illustrations/search/mr%%20krabs%%20spongebob/
)

if exist "%drawable_path%\puzzle_image_5.jpg" (
    echo ✅ puzzle_image_5.jpg 已找到 ^(珊迪^)
) else if exist "%drawable_path%\puzzle_image_5.png" (
    echo ✅ puzzle_image_5.png 已找到 ^(珊迪^)
) else (
    echo ❌ puzzle_image_5 未找到 ^(珊迪^)
    echo    请下载: https://pixabay.com/illustrations/search/sandy%%20cheeks%%20spongebob/
)

if exist "%drawable_path%\puzzle_image_6.jpg" (
    echo ✅ puzzle_image_6.jpg 已找到 ^(小蜗^)
) else if exist "%drawable_path%\puzzle_image_6.png" (
    echo ✅ puzzle_image_6.png 已找到 ^(小蜗^)
) else (
    echo ❌ puzzle_image_6 未找到 ^(小蜗^)
    echo    请下载: https://pixabay.com/illustrations/search/gary%%20snail%%20spongebob/
)

if exist "%drawable_path%\puzzle_image_7.jpg" (
    echo ✅ puzzle_image_7.jpg 已找到 ^(痞老板^)
) else if exist "%drawable_path%\puzzle_image_7.png" (
    echo ✅ puzzle_image_7.png 已找到 ^(痞老板^)
) else (
    echo ❌ puzzle_image_7 未找到 ^(痞老板^)
    echo    请下载: https://pixabay.com/illustrations/search/plankton%%20spongebob/
)

if exist "%drawable_path%\puzzle_image_8.jpg" (
    echo ✅ puzzle_image_8.jpg 已找到 ^(泡芙老师^)
) else if exist "%drawable_path%\puzzle_image_8.png" (
    echo ✅ puzzle_image_8.png 已找到 ^(泡芙老师^)
) else (
    echo ❌ puzzle_image_8 未找到 ^(泡芙老师^)
    echo    请下载: https://pixabay.com/illustrations/search/mrs%%20puff%%20spongebob/
)

if exist "%drawable_path%\puzzle_image_9.jpg" (
    echo ✅ puzzle_image_9.jpg 已找到 ^(皮老板^)
) else if exist "%drawable_path%\puzzle_image_9.png" (
    echo ✅ puzzle_image_9.png 已找到 ^(皮老板^)
) else (
    echo ❌ puzzle_image_9 未找到 ^(皮老板^)
    echo    请下载: https://pixabay.com/illustrations/search/larry%%20lobster%%20spongebob/
)

if exist "%drawable_path%\puzzle_image_10.jpg" (
    echo ✅ puzzle_image_10.jpg 已找到 ^(比奇堡^)
) else if exist "%drawable_path%\puzzle_image_10.png" (
    echo ✅ puzzle_image_10.png 已找到 ^(比奇堡^)
) else (
    echo ❌ puzzle_image_10 未找到 ^(比奇堡^)
    echo    请下载: https://pixabay.com/illustrations/search/bikini%%20bottom%%20spongebob/
)

echo.
echo 📋 下载指南:
echo.
echo 1. 访问上面提供的链接
echo 2. 选择海绵宝宝角色图片
echo 3. 下载并重命名为 puzzle_image_X.png
echo 4. 放入 %drawable_path% 目录
echo 5. 运行 gradlew build 重新构建项目
echo.

REM 检查是否所有图片都存在
set "all_found=true"
if not exist "%drawable_path%\puzzle_image_1.jpg" if not exist "%drawable_path%\puzzle_image_1.png" set "all_found=false"
if not exist "%drawable_path%\puzzle_image_2.jpg" if not exist "%drawable_path%\puzzle_image_2.png" set "all_found=false"
if not exist "%drawable_path%\puzzle_image_3.jpg" if not exist "%drawable_path%\puzzle_image_3.png" set "all_found=false"
if not exist "%drawable_path%\puzzle_image_4.jpg" if not exist "%drawable_path%\puzzle_image_4.png" set "all_found=false"
if not exist "%drawable_path%\puzzle_image_5.jpg" if not exist "%drawable_path%\puzzle_image_5.png" set "all_found=false"
if not exist "%drawable_path%\puzzle_image_6.jpg" if not exist "%drawable_path%\puzzle_image_6.png" set "all_found=false"
if not exist "%drawable_path%\puzzle_image_7.jpg" if not exist "%drawable_path%\puzzle_image_7.png" set "all_found=false"
if not exist "%drawable_path%\puzzle_image_8.jpg" if not exist "%drawable_path%\puzzle_image_8.png" set "all_found=false"
if not exist "%drawable_path%\puzzle_image_9.jpg" if not exist "%drawable_path%\puzzle_image_9.png" set "all_found=false"
if not exist "%drawable_path%\puzzle_image_10.jpg" if not exist "%drawable_path%\puzzle_image_10.png" set "all_found=false"

if "%all_found%"=="true" (
    echo 🎉 所有10张海绵宝宝图片都已找到！
    echo 💡 现在可以运行: gradlew build
) else (
    echo ⚠️  还有图片文件缺失，请按照上面的指南下载
)

echo.
pause
