<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="@color/background_color"
    android:padding="32dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="拼图游戏"
        android:textSize="36sp"
        android:textStyle="bold"
        android:textColor="@color/primary_text_color"
        android:layout_marginBottom="48dp" />

    <ImageView
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:src="@drawable/game_logo"
        android:layout_marginBottom="48dp"
        android:scaleType="centerCrop" />

    <Button
        android:id="@+id/btn_start_game"
        android:layout_width="200dp"
        android:layout_height="56dp"
        android:text="开始游戏"
        android:textSize="18sp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/button_primary"
        android:textColor="@android:color/white" />

    <Button
        android:id="@+id/btn_level_select"
        android:layout_width="200dp"
        android:layout_height="56dp"
        android:text="选择关卡"
        android:textSize="18sp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/button_secondary"
        android:textColor="@color/primary_text_color" />

    <Button
        android:id="@+id/btn_exit"
        android:layout_width="200dp"
        android:layout_height="56dp"
        android:text="退出游戏"
        android:textSize="18sp"
        android:background="@drawable/button_secondary"
        android:textColor="@color/primary_text_color" />

</LinearLayout>
