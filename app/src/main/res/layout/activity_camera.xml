<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/header_background"
        android:gravity="center_vertical">

        <Button
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/ic_back"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="拍照拼图 📸"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text_color"
            android:gravity="center" />

        <!-- 占位符，保持标题居中 -->
        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />

    </LinearLayout>

    <!-- 说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="拍摄多张照片，制作专属的拼图游戏！📷✨"
        android:textSize="16sp"
        android:textColor="@color/secondary_text_color"
        android:gravity="center"
        android:padding="16dp"
        android:background="@color/footer_background" />

    <!-- 照片数量显示 -->
    <TextView
        android:id="@+id/tv_photo_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="已拍摄 0 张照片"
        android:textSize="16sp"
        android:textColor="@color/primary_text_color"
        android:gravity="center"
        android:padding="8dp"
        android:background="@color/background_color" />

    <!-- 网格设置区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:background="@color/background_color">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="横格数量："
            android:textSize="16sp"
            android:textColor="@color/primary_text_color"
            android:layout_marginEnd="12dp" />

        <Spinner
            android:id="@+id/spinner_grid_size"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:layout_marginEnd="16dp" />

        <TextView
            android:id="@+id/tv_grid_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="横格：3格，纵格将根据照片比例自动计算"
            android:textSize="14sp"
            android:textColor="@color/secondary_text_color" />

    </LinearLayout>

    <!-- 照片列表区域 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_photos"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="16dp"
        android:background="@color/background_color" />

    <!-- 操作按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/footer_background">

        <!-- 主要操作按钮 -->
        <Button
            android:id="@+id/btn_take_photo"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="拍照 📸"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="@drawable/button_primary"
            android:textColor="@android:color/white"
            android:layout_marginBottom="12dp" />

        <!-- 开始拼图按钮 -->
        <Button
            android:id="@+id/btn_start_puzzle"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="请先拍照"
            android:textSize="16sp"
            android:background="@drawable/button_primary"
            android:textColor="@android:color/white"
            android:enabled="false" />

    </LinearLayout>

</LinearLayout>
