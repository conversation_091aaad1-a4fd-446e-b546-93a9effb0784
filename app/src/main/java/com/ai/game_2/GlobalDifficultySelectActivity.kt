package com.ai.game_2

import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class GlobalDifficultySelectActivity : AppCompatActivity() {
    
    private lateinit var titleText: TextView
    private lateinit var difficultyRecyclerView: RecyclerView
    private lateinit var backButton: Button
    private lateinit var currentStatsText: TextView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_global_difficulty_select)
        
        initializeViews()
        setupRecyclerView()
        setupClickListeners()
        updateStatsDisplay()
    }
    
    private fun initializeViews() {
        titleText = findViewById(R.id.tv_title)
        difficultyRecyclerView = findViewById(R.id.rv_difficulty)
        backButton = findViewById(R.id.btn_back)
        currentStatsText = findViewById(R.id.tv_current_stats)
        
        titleText.text = "🎯 设置全局游戏难度"
    }
    
    private fun setupRecyclerView() {
        val currentDifficulty = GlobalDifficultyManager.getCurrentDifficulty()
        val difficultyAdapter = GlobalDifficultyAdapter(
            difficulties = DifficultyLevel.getAllLevels(),
            currentDifficulty = currentDifficulty
        ) { difficulty ->
            selectDifficulty(difficulty)
        }
        
        difficultyRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@GlobalDifficultySelectActivity)
            adapter = difficultyAdapter
        }
    }
    
    private fun setupClickListeners() {
        backButton.setOnClickListener {
            finish()
        }
    }
    
    private fun selectDifficulty(difficulty: DifficultyLevel) {
        // 设置全局难度
        GlobalDifficultyManager.setGlobalDifficulty(difficulty)
        
        // 更新统计显示
        updateStatsDisplay()
        
        // 重新设置适配器以更新选中状态
        setupRecyclerView()
        
        // 显示设置成功的提示
        android.widget.Toast.makeText(
            this, 
            "已设置全局难度为：${difficulty.displayName}", 
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }
    
    private fun updateStatsDisplay() {
        val stats = GlobalDifficultyManager.getDifficultyStats()
        currentStatsText.text = "📊 当前设置效果：\n$stats"
    }
}

/**
 * 全局难度选择适配器
 */
class GlobalDifficultyAdapter(
    private val difficulties: List<DifficultyLevel>,
    private val currentDifficulty: DifficultyLevel,
    private val onDifficultySelected: (DifficultyLevel) -> Unit
) : RecyclerView.Adapter<GlobalDifficultyAdapter.GlobalDifficultyViewHolder>() {
    
    class GlobalDifficultyViewHolder(itemView: android.view.View) : RecyclerView.ViewHolder(itemView) {
        val nameText: TextView = itemView.findViewById(R.id.tv_difficulty_name)
        val descriptionText: TextView = itemView.findViewById(R.id.tv_difficulty_description)
        val gridInfoText: TextView = itemView.findViewById(R.id.tv_grid_info)
        val selectButton: Button = itemView.findViewById(R.id.btn_select_difficulty)
        val selectedIndicator: TextView = itemView.findViewById(R.id.tv_selected_indicator)
    }
    
    override fun onCreateViewHolder(parent: android.view.ViewGroup, viewType: Int): GlobalDifficultyViewHolder {
        val view = android.view.LayoutInflater.from(parent.context)
            .inflate(R.layout.item_global_difficulty, parent, false)
        return GlobalDifficultyViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: GlobalDifficultyViewHolder, position: Int) {
        val difficulty = difficulties[position]
        val isSelected = difficulty == currentDifficulty
        
        holder.nameText.text = difficulty.displayName
        holder.descriptionText.text = difficulty.description
        
        // 显示详细的网格信息
        val baseSize = difficulty.baseGridSize
        val squarePieces = baseSize * baseSize
        val horizontalPieces = (baseSize + 1) * baseSize
        val verticalPieces = baseSize * (baseSize + 1)
        
        holder.gridInfoText.text = """
            基础网格: ${baseSize}×${baseSize} (${squarePieces}块)
            横向图片: ${baseSize + 1}×${baseSize} (${horizontalPieces}块)
            纵向图片: ${baseSize}×${baseSize + 1} (${verticalPieces}块)
            
            ✨ 所有拼图都会根据图片比例自动调整网格大小
        """.trimIndent()
        
        // 设置难度颜色
        val colorRes = when (difficulty) {
            DifficultyLevel.EASY -> android.graphics.Color.parseColor("#4CAF50")
            DifficultyLevel.MEDIUM -> android.graphics.Color.parseColor("#2196F3")
            DifficultyLevel.HARD -> android.graphics.Color.parseColor("#F44336")
        }
        holder.nameText.setTextColor(colorRes)
        
        // 显示选中状态
        if (isSelected) {
            holder.selectedIndicator.visibility = android.view.View.VISIBLE
            holder.selectedIndicator.text = "✅ 当前选择"
            holder.selectButton.text = "已选择"
            holder.selectButton.isEnabled = false
            holder.selectButton.alpha = 0.6f
            
            // 高亮选中的卡片
            holder.itemView.setBackgroundColor(android.graphics.Color.parseColor("#E3F2FD"))
        } else {
            holder.selectedIndicator.visibility = android.view.View.GONE
            holder.selectButton.text = "选择此难度"
            holder.selectButton.isEnabled = true
            holder.selectButton.alpha = 1.0f
            
            // 普通卡片背景
            holder.itemView.setBackgroundColor(android.graphics.Color.WHITE)
        }
        
        holder.selectButton.setOnClickListener {
            onDifficultySelected(difficulty)
        }
        
        // 设置点击整个item也可以选择
        holder.itemView.setOnClickListener {
            if (!isSelected) {
                onDifficultySelected(difficulty)
            }
        }
    }
    
    override fun getItemCount(): Int = difficulties.size
}
