package com.ai.game_2

import android.graphics.*
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.cos
import kotlin.math.sin

class CuteImageGenerator {
    
    companion object {
        private const val TAG = "CuteImageGenerator"
        
        // 可爱主题的搜索关键词
        private val cuteThemes = listOf(
            "可爱小猫简笔画", "可爱小狗简笔画", "可爱小兔子简笔画", "可爱小熊简笔画",
            "可爱小鸟简笔画", "可爱小鱼简笔画", "可爱蝴蝶简笔画", "可爱花朵简笔画",
            "可爱太阳简笔画", "可爱月亮简笔画", "可爱星星简笔画", "可爱彩虹简笔画",
            "可爱房子简笔画", "可爱汽车简笔画", "可爱飞机简笔画", "可爱船简笔画",
            "可爱蛋糕简笔画", "可爱冰淇淋简笔画", "可爱水果简笔画", "可爱蔬菜简笔画"
        )
        
        // 百度图片搜索API（需要替换为实际的API）
        private fun getBaiduImageSearchUrl(keyword: String): String {
            return "https://image.baidu.com/search/acjson?tn=resultjson_com&logid=&ipn=rj&ct=201326592&is=&fp=result&queryWord=${keyword}&cl=2&lm=-1&ie=utf-8&oe=utf-8&adpicid=&st=-1&z=&ic=0&hd=&latest=&copyright=&word=${keyword}&s=&se=&tab=&width=&height=&face=0&istype=2&qc=&nc=1&fr=&expermode=&force=&pn=30&rn=30"
        }
    }
    
    /**
     * 生成可爱主题的拼图图片
     */
    suspend fun generateCuteImage(level: Int, targetWidth: Int, targetHeight: Int, cacheDir: File): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 首先尝试从网络下载图片
                val downloadedBitmap = downloadCuteImage(level, targetWidth, targetHeight, cacheDir)
                if (downloadedBitmap != null) {
                    Log.d(TAG, "Successfully downloaded cute image for level $level")
                    return@withContext downloadedBitmap
                }
                
                // 如果下载失败，生成本地图片
                Log.d(TAG, "Download failed, generating local cute image for level $level")
                generateLocalCuteImage(level, targetWidth, targetHeight)
            } catch (e: Exception) {
                Log.e(TAG, "Error generating cute image for level $level", e)
                // 出错时生成本地图片作为备用
                generateLocalCuteImage(level, targetWidth, targetHeight)
            }
        }
    }
    
    /**
     * 从网络下载可爱图片
     */
    private suspend fun downloadCuteImage(level: Int, targetWidth: Int, targetHeight: Int, cacheDir: File): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 根据关卡选择主题
                val themeIndex = (level - 1) % cuteThemes.size
                val keyword = cuteThemes[themeIndex]
                
                // 检查缓存
                val cacheFile = File(cacheDir, "cute_image_${level}.jpg")
                if (cacheFile.exists()) {
                    Log.d(TAG, "Loading cached image for level $level")
                    val bitmap = BitmapFactory.decodeFile(cacheFile.absolutePath)
                    if (bitmap != null) {
                        return@withContext resizeBitmap(bitmap, targetWidth, targetHeight)
                    }
                }
                
                // 尝试多个下载源
                val downloadSources = listOf(
                    "https://image.baidu.com/search/down?tn=download&word=${keyword}&ie=utf8",
                    "https://cn.bing.com/images/search?q=${keyword}&form=HDRSC2",
                    "https://www.google.com/search?q=${keyword}&tbm=isch"
                )
                
                for (source in downloadSources) {
                    try {
                        val bitmap = downloadImageFromUrl(source, targetWidth, targetHeight)
                        if (bitmap != null) {
                            // 保存到缓存
                            saveBitmapToCache(bitmap, cacheFile)
                            return@withContext bitmap
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "Failed to download from $source", e)
                        continue
                    }
                }
                
                null
            } catch (e: Exception) {
                Log.e(TAG, "Error downloading cute image", e)
                null
            }
        }
    }
    
    /**
     * 从URL下载图片
     */
    private suspend fun downloadImageFromUrl(imageUrl: String, targetWidth: Int, targetHeight: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val url = URL(imageUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.connectTimeout = 10000
                connection.readTimeout = 15000
                connection.doInput = true
                connection.connect()
                
                if (connection.responseCode == HttpURLConnection.HTTP_OK) {
                    val inputStream = connection.inputStream
                    val bitmap = BitmapFactory.decodeStream(inputStream)
                    inputStream.close()
                    
                    if (bitmap != null) {
                        return@withContext resizeBitmap(bitmap, targetWidth, targetHeight)
                    }
                }
                
                connection.disconnect()
                null
            } catch (e: Exception) {
                Log.e(TAG, "Error downloading image from $imageUrl", e)
                null
            }
        }
    }
    
    /**
     * 保存图片到缓存
     */
    private fun saveBitmapToCache(bitmap: Bitmap, cacheFile: File) {
        try {
            val outputStream = FileOutputStream(cacheFile)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 85, outputStream)
            outputStream.close()
            Log.d(TAG, "Saved image to cache: ${cacheFile.absolutePath}")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving bitmap to cache", e)
        }
    }
    
    /**
     * 生成本地可爱图片（备用方案）
     */
    private fun generateLocalCuteImage(level: Int, targetWidth: Int, targetHeight: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(targetWidth, targetHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint().apply {
            isAntiAlias = true
        }
        
        // 绘制背景
        drawBackground(canvas, paint, targetWidth, targetHeight)
        
        // 根据关卡绘制不同的可爱元素
        val centerX = targetWidth / 2f
        val centerY = targetHeight / 2f
        
        when ((level - 1) % 6) {
            0 -> drawCuteCat(canvas, paint, centerX, centerY)
            1 -> drawCuteDog(canvas, paint, centerX, centerY)
            2 -> drawCuteBear(canvas, paint, centerX, centerY)
            3 -> drawCuteSun(canvas, paint, centerX, centerY)
            4 -> drawCuteHeart(canvas, paint, centerX, centerY)
            5 -> drawCuteHouse(canvas, paint, centerX, centerY)
        }
        
        return bitmap
    }
    
    /**
     * 绘制背景
     */
    private fun drawBackground(canvas: Canvas, paint: Paint, width: Int, height: Int) {
        // 渐变背景
        val gradient = LinearGradient(
            0f, 0f, 0f, height.toFloat(),
            Color.parseColor("#FFE4E1"), Color.parseColor("#FFF8DC"),
            Shader.TileMode.CLAMP
        )
        paint.shader = gradient
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), paint)
        paint.shader = null
        
        // 添加一些装饰性的小元素
        paint.color = Color.parseColor("#FFB6C1")
        paint.alpha = 100
        for (i in 0..5) {
            val x = (width * 0.1f + i * width * 0.15f)
            val y = (height * 0.1f + (i % 2) * height * 0.8f)
            canvas.drawCircle(x, y, 20f, paint)
        }
        paint.alpha = 255
    }
    
    /**
     * 调整图片尺寸
     */
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        if (bitmap.width >= targetWidth && bitmap.height >= targetHeight) {
            // 计算缩放比例，保持宽高比
            val scaleX = targetWidth.toFloat() / bitmap.width
            val scaleY = targetHeight.toFloat() / bitmap.height
            val scale = maxOf(scaleX, scaleY)
            
            val scaledWidth = (bitmap.width * scale).toInt()
            val scaledHeight = (bitmap.height * scale).toInt()
            
            val scaledBitmap = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true)
            
            // 裁剪到目标尺寸
            val startX = (scaledWidth - targetWidth) / 2
            val startY = (scaledHeight - targetHeight) / 2
            
            return Bitmap.createBitmap(scaledBitmap, startX, startY, targetWidth, targetHeight)
        } else {
            // 原图太小，直接缩放
            return Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
        }
    }

    /**
     * 绘制可爱小猫
     */
    private fun drawCuteCat(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 猫头 - 橙色
        paint.color = Color.parseColor("#FFA500")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 80f, paint)

        // 猫耳朵 - 橙色
        val earPath = Path()
        earPath.moveTo(centerX - 60f, centerY - 40f)
        earPath.lineTo(centerX - 40f, centerY - 80f)
        earPath.lineTo(centerX - 20f, centerY - 40f)
        earPath.close()
        canvas.drawPath(earPath, paint)

        earPath.reset()
        earPath.moveTo(centerX + 20f, centerY - 40f)
        earPath.lineTo(centerX + 40f, centerY - 80f)
        earPath.lineTo(centerX + 60f, centerY - 40f)
        earPath.close()
        canvas.drawPath(earPath, paint)

        // 眼睛 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 25f, centerY - 20f, 12f, paint)
        canvas.drawCircle(centerX + 25f, centerY - 20f, 12f, paint)

        // 眼睛高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 20f, centerY - 25f, 4f, paint)
        canvas.drawCircle(centerX + 30f, centerY - 25f, 4f, paint)

        // 鼻子 - 粉色
        paint.color = Color.parseColor("#FFB6C1")
        val nosePath = Path()
        nosePath.moveTo(centerX, centerY)
        nosePath.lineTo(centerX - 8f, centerY + 10f)
        nosePath.lineTo(centerX + 8f, centerY + 10f)
        nosePath.close()
        canvas.drawPath(nosePath, paint)

        // 嘴巴 - 黑色
        paint.color = Color.BLACK
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 3f
        val mouthPath = Path()
        mouthPath.moveTo(centerX, centerY + 10f)
        mouthPath.quadTo(centerX - 15f, centerY + 25f, centerX - 30f, centerY + 15f)
        canvas.drawPath(mouthPath, paint)

        mouthPath.reset()
        mouthPath.moveTo(centerX, centerY + 10f)
        mouthPath.quadTo(centerX + 15f, centerY + 25f, centerX + 30f, centerY + 15f)
        canvas.drawPath(mouthPath, paint)

        // 胡须 - 黑色
        paint.strokeWidth = 2f
        canvas.drawLine(centerX - 60f, centerY - 5f, centerX - 30f, centerY, paint)
        canvas.drawLine(centerX - 60f, centerY + 5f, centerX - 30f, centerY + 10f, paint)
        canvas.drawLine(centerX + 30f, centerY, centerX + 60f, centerY - 5f, paint)
        canvas.drawLine(centerX + 30f, centerY + 10f, centerX + 60f, centerY + 5f, paint)
    }

    /**
     * 绘制可爱小狗
     */
    private fun drawCuteDog(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 狗头 - 棕色
        paint.color = Color.parseColor("#D2691E")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 80f, paint)

        // 狗耳朵 - 深棕色
        paint.color = Color.parseColor("#8B4513")
        canvas.drawOval(centerX - 80f, centerY - 60f, centerX - 40f, centerY - 10f, paint)
        canvas.drawOval(centerX + 40f, centerY - 60f, centerX + 80f, centerY - 10f, paint)

        // 眼睛 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 25f, centerY - 20f, 12f, paint)
        canvas.drawCircle(centerX + 25f, centerY - 20f, 12f, paint)

        // 眼睛高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 20f, centerY - 25f, 4f, paint)
        canvas.drawCircle(centerX + 30f, centerY - 25f, 4f, paint)

        // 鼻子 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX, centerY + 5f, 8f, paint)

        // 嘴巴 - 黑色
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 4f
        val mouthPath = Path()
        mouthPath.moveTo(centerX, centerY + 13f)
        mouthPath.quadTo(centerX - 20f, centerY + 35f, centerX - 40f, centerY + 25f)
        canvas.drawPath(mouthPath, paint)

        mouthPath.reset()
        mouthPath.moveTo(centerX, centerY + 13f)
        mouthPath.quadTo(centerX + 20f, centerY + 35f, centerX + 40f, centerY + 25f)
        canvas.drawPath(mouthPath, paint)

        // 舌头 - 粉色
        paint.color = Color.parseColor("#FF69B4")
        paint.style = Paint.Style.FILL
        canvas.drawOval(centerX - 15f, centerY + 20f, centerX + 15f, centerY + 40f, paint)
    }

    /**
     * 绘制可爱小熊
     */
    private fun drawCuteBear(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 熊头 - 棕色
        paint.color = Color.parseColor("#8B4513")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 80f, paint)

        // 熊耳朵 - 棕色
        canvas.drawCircle(centerX - 50f, centerY - 50f, 30f, paint)
        canvas.drawCircle(centerX + 50f, centerY - 50f, 30f, paint)

        // 耳朵内部 - 浅棕色
        paint.color = Color.parseColor("#D2691E")
        canvas.drawCircle(centerX - 50f, centerY - 50f, 15f, paint)
        canvas.drawCircle(centerX + 50f, centerY - 50f, 15f, paint)

        // 眼睛 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 25f, centerY - 20f, 12f, paint)
        canvas.drawCircle(centerX + 25f, centerY - 20f, 12f, paint)

        // 眼睛高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 20f, centerY - 25f, 4f, paint)
        canvas.drawCircle(centerX + 30f, centerY - 25f, 4f, paint)

        // 鼻子 - 黑色
        paint.color = Color.BLACK
        canvas.drawOval(centerX - 8f, centerY - 5f, centerX + 8f, centerY + 8f, paint)

        // 嘴巴 - 黑色
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 4f
        val mouthPath = Path()
        mouthPath.moveTo(centerX, centerY + 8f)
        mouthPath.quadTo(centerX - 20f, centerY + 30f, centerX - 35f, centerY + 20f)
        canvas.drawPath(mouthPath, paint)

        mouthPath.reset()
        mouthPath.moveTo(centerX, centerY + 8f)
        mouthPath.quadTo(centerX + 20f, centerY + 30f, centerX + 35f, centerY + 20f)
        canvas.drawPath(mouthPath, paint)
    }

    /**
     * 绘制可爱太阳
     */
    private fun drawCuteSun(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 太阳主体 - 金黄色
        paint.color = Color.parseColor("#FFD700")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 80f, paint)

        // 阳光射线 - 橙色
        paint.color = Color.parseColor("#FFA500")
        paint.strokeWidth = 6f
        paint.style = Paint.Style.STROKE
        paint.strokeCap = Paint.Cap.ROUND

        for (i in 0..11) {
            val angle = i * Math.PI / 6
            val startX = centerX + (100f * cos(angle)).toFloat()
            val startY = centerY + (100f * sin(angle)).toFloat()
            val endX = centerX + (130f * cos(angle)).toFloat()
            val endY = centerY + (130f * sin(angle)).toFloat()
            canvas.drawLine(startX, startY, endX, endY, paint)
        }

        // 眼睛 - 黑色
        paint.style = Paint.Style.FILL
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 25f, centerY - 20f, 10f, paint)
        canvas.drawCircle(centerX + 25f, centerY - 20f, 10f, paint)

        // 眼睛高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 20f, centerY - 25f, 3f, paint)
        canvas.drawCircle(centerX + 30f, centerY - 25f, 3f, paint)

        // 腮红 - 粉色
        paint.color = Color.parseColor("#FFB6C1")
        canvas.drawCircle(centerX - 60f, centerY + 10f, 12f, paint)
        canvas.drawCircle(centerX + 60f, centerY + 10f, 12f, paint)

        // 嘴巴 - 红色弧线
        paint.color = Color.parseColor("#FF4500")
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 5f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 30f, centerY + 15f)
        mouthPath.quadTo(centerX, centerY + 40f, centerX + 30f, centerY + 15f)
        canvas.drawPath(mouthPath, paint)
    }

    /**
     * 绘制可爱爱心
     */
    private fun drawCuteHeart(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 爱心 - 粉色
        paint.color = Color.parseColor("#FF69B4")
        paint.style = Paint.Style.FILL

        val path = Path()
        path.moveTo(centerX, centerY + 60f)
        path.cubicTo(centerX - 60f, centerY - 30f, centerX - 90f, centerY - 90f, centerX - 30f, centerY - 90f)
        path.cubicTo(centerX - 15f, centerY - 105f, centerX + 15f, centerY - 105f, centerX + 30f, centerY - 90f)
        path.cubicTo(centerX + 90f, centerY - 90f, centerX + 60f, centerY - 30f, centerX, centerY + 60f)
        canvas.drawPath(path, paint)

        // 爱心高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 20f, centerY - 45f, 12f, paint)
        canvas.drawCircle(centerX + 20f, centerY - 45f, 8f, paint)

        // 眼睛 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 15f, centerY - 20f, 6f, paint)
        canvas.drawCircle(centerX + 15f, centerY - 20f, 6f, paint)

        // 嘴巴 - 红色
        paint.color = Color.parseColor("#FF4500")
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 3f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 10f, centerY)
        mouthPath.quadTo(centerX, centerY + 10f, centerX + 10f, centerY)
        canvas.drawPath(mouthPath, paint)
    }

    /**
     * 绘制可爱房子
     */
    private fun drawCuteHouse(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 房子主体 - 黄色
        paint.color = Color.parseColor("#FFFF99")
        paint.style = Paint.Style.FILL
        canvas.drawRect(centerX - 60f, centerY, centerX + 60f, centerY + 90f, paint)

        // 屋顶 - 红色
        paint.color = Color.parseColor("#FF6347")
        val roofPath = Path()
        roofPath.moveTo(centerX - 75f, centerY)
        roofPath.lineTo(centerX, centerY - 60f)
        roofPath.lineTo(centerX + 75f, centerY)
        roofPath.close()
        canvas.drawPath(roofPath, paint)

        // 门 - 棕色
        paint.color = Color.parseColor("#8B4513")
        canvas.drawRect(centerX - 15f, centerY + 45f, centerX + 15f, centerY + 90f, paint)

        // 门把手 - 金色
        paint.color = Color.parseColor("#FFD700")
        canvas.drawCircle(centerX + 10f, centerY + 67f, 2f, paint)

        // 窗户 - 蓝色
        paint.color = Color.parseColor("#87CEEB")
        canvas.drawRect(centerX - 45f, centerY + 22f, centerX - 22f, centerY + 45f, paint)
        canvas.drawRect(centerX + 22f, centerY + 22f, centerX + 45f, centerY + 45f, paint)

        // 窗框 - 白色
        paint.color = Color.WHITE
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 2f
        canvas.drawRect(centerX - 45f, centerY + 22f, centerX - 22f, centerY + 45f, paint)
        canvas.drawRect(centerX + 22f, centerY + 22f, centerX + 45f, centerY + 45f, paint)

        // 窗户十字
        canvas.drawLine(centerX - 33.5f, centerY + 22f, centerX - 33.5f, centerY + 45f, paint)
        canvas.drawLine(centerX - 45f, centerY + 33.5f, centerX - 22f, centerY + 33.5f, paint)
        canvas.drawLine(centerX + 33.5f, centerY + 22f, centerX + 33.5f, centerY + 45f, paint)
        canvas.drawLine(centerX + 22f, centerY + 33.5f, centerX + 45f, centerY + 33.5f, paint)

        // 房子眼睛 - 黑色
        paint.style = Paint.Style.FILL
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 30f, centerY - 20f, 4f, paint)
        canvas.drawCircle(centerX + 30f, centerY - 20f, 4f, paint)

        // 房子嘴巴 - 红色
        paint.color = Color.parseColor("#FF4500")
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 3f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 15f, centerY - 5f)
        mouthPath.quadTo(centerX, centerY + 5f, centerX + 15f, centerY - 5f)
        canvas.drawPath(mouthPath, paint)
    }
}
