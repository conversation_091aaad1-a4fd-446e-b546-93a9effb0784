@echo off
echo 正在替换拼图图片...

REM 删除旧的XML矢量图文件
echo 删除旧的XML文件...
del "app\src\main\res\drawable\puzzle_image_1.xml" 2>nul
del "app\src\main\res\drawable\puzzle_image_2.xml" 2>nul
del "app\src\main\res\drawable\puzzle_image_3.xml" 2>nul
del "app\src\main\res\drawable\puzzle_image_4.xml" 2>nul
del "app\src\main\res\drawable\puzzle_image_5.xml" 2>nul

echo 旧文件已删除！

echo.
echo 请将下载的PNG图片重命名并放入以下位置：
echo.
echo 1. puzzle_image_1.png (可爱小猫) -> app\src\main\res\drawable\
echo 2. puzzle_image_2.png (萌萌小狗) -> app\src\main\res\drawable\
echo 3. puzzle_image_3.png (呆萌熊猫) -> app\src\main\res\drawable\
echo 4. puzzle_image_4.png (粉嫩兔子) -> app\src\main\res\drawable\
echo 5. puzzle_image_5.png (企鹅宝宝) -> app\src\main\res\drawable\
echo.

REM 检查文件是否存在
echo 检查图片文件...
if exist "app\src\main\res\drawable\puzzle_image_1.png" (
    echo ✓ puzzle_image_1.png 已找到
) else (
    echo ✗ puzzle_image_1.png 未找到
)

if exist "app\src\main\res\drawable\puzzle_image_2.png" (
    echo ✓ puzzle_image_2.png 已找到
) else (
    echo ✗ puzzle_image_2.png 未找到
)

if exist "app\src\main\res\drawable\puzzle_image_3.png" (
    echo ✓ puzzle_image_3.png 已找到
) else (
    echo ✗ puzzle_image_3.png 未找到
)

if exist "app\src\main\res\drawable\puzzle_image_4.png" (
    echo ✓ puzzle_image_4.png 已找到
) else (
    echo ✗ puzzle_image_4.png 未找到
)

if exist "app\src\main\res\drawable\puzzle_image_5.png" (
    echo ✓ puzzle_image_5.png 已找到
) else (
    echo ✗ puzzle_image_5.png 未找到
)

echo.
echo 完成后请运行: gradlew build
echo.
pause
