import urllib.request
import os

def download_image(url, filename):
    try:
        # 确保目录存在
        os.makedirs('app/src/main/res/drawable', exist_ok=True)
        
        # 下载图片
        filepath = f'app/src/main/res/drawable/{filename}'
        urllib.request.urlretrieve(url, filepath)
        print(f"✅ 成功下载: {filename}")
        return True
    except Exception as e:
        print(f"❌ 下载失败 {filename}: {e}")
        return False

# OpenClipart的一些公主图片直接链接
images = [
    {
        'url': 'https://openclipart.org/image/800px/124225',
        'filename': 'puzzle_image_1.png'
    },
    {
        'url': 'https://openclipart.org/image/800px/293782', 
        'filename': 'puzzle_image_2.png'
    },
    {
        'url': 'https://openclipart.org/image/800px/343753',
        'filename': 'puzzle_image_3.png'
    }
]

print("🎨 开始下载公主图片...")
for img in images:
    download_image(img['url'], img['filename'])

print("✨ 下载完成！")
