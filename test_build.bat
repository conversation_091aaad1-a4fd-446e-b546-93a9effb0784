@echo off
echo 🔧 测试项目编译...
echo.

echo 📋 检查资源文件...
if exist "app\src\main\res\values\colors.xml" (
    echo ✅ colors.xml 存在
) else (
    echo ❌ colors.xml 不存在
    exit /b 1
)

if exist "app\src\main\res\layout\activity_difficulty_select.xml" (
    echo ✅ activity_difficulty_select.xml 存在
) else (
    echo ❌ activity_difficulty_select.xml 不存在
    exit /b 1
)

if exist "app\src\main\res\layout\item_difficulty.xml" (
    echo ✅ item_difficulty.xml 存在
) else (
    echo ❌ item_difficulty.xml 不存在
    exit /b 1
)

if exist "app\src\main\java\com\ai\game_2\DifficultySelectActivity.kt" (
    echo ✅ DifficultySelectActivity.kt 存在
) else (
    echo ❌ DifficultySelectActivity.kt 不存在
    exit /b 1
)

if exist "app\src\main\java\com\ai\game_2\DifficultyLevel.kt" (
    echo ✅ DifficultyLevel.kt 存在
) else (
    echo ❌ DifficultyLevel.kt 不存在
    exit /b 1
)

echo.
echo 📱 所有必需的资源文件都存在！
echo.
echo 💡 提示：
echo - 所有颜色资源已添加到 colors.xml
echo - DifficultySelectActivity 已注册到 AndroidManifest.xml
echo - 难度系统相关的类文件已创建
echo.
echo 🚀 现在可以尝试在 Android Studio 中编译项目
echo.
pause
