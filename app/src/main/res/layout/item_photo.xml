<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_photo"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:scaleType="centerCrop"
            android:background="@color/light_gray" />

        <ImageButton
            android:id="@+id/btn_delete"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_margin="4dp"
            android:background="@drawable/button_round_red"
            android:src="@android:drawable/ic_menu_delete"
            android:contentDescription="删除照片" />

        <TextView
            android:id="@+id/tv_level"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_margin="4dp"
            android:background="@drawable/button_round"
            android:padding="4dp"
            android:text="第1关"
            android:textColor="@color/white"
            android:textSize="12sp" />

    </RelativeLayout>

</androidx.cardview.widget.CardView>
