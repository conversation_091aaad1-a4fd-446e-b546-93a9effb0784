<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="320dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background"
    android:padding="24dp"
    android:gravity="center">

    <!-- 标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="语音输入"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/primary_text_color"
        android:layout_marginBottom="16dp" />

    <!-- 麦克风图标 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="🎤"
        android:textSize="48sp"
        android:layout_marginBottom="16dp" />

    <!-- 语音波形视图 -->
    <com.ai.game_2.VoiceWaveView
        android:id="@+id/voice_wave_view"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginBottom="16dp" />

    <!-- 状态文字 -->
    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="请说话..."
        android:textSize="16sp"
        android:textColor="@color/secondary_text_color"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 取消按钮 -->
    <Button
        android:id="@+id/btn_cancel"
        android:layout_width="120dp"
        android:layout_height="48dp"
        android:text="取消"
        android:textSize="16sp"
        android:background="@drawable/button_secondary"
        android:textColor="@color/primary_text_color" />

</LinearLayout>
