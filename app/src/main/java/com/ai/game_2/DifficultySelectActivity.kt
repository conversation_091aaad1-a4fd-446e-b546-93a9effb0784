package com.ai.game_2

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class DifficultySelectActivity : AppCompatActivity() {
    
    private lateinit var titleText: TextView
    private lateinit var difficultyRecyclerView: RecyclerView
    private lateinit var backButton: Button
    
    private var selectedTheme: GameTheme = GameTheme.CUTE
    private var selectedLevel: Int = 1
    private var imageUrl: String? = null

    // 语音搜图主题的额外参数
    private var searchQuery: String? = null
    private var imageUrls: ArrayList<String>? = null
    private var useDynamicGrid: Boolean = false

    // 拍照主题的额外参数
    private var customPhotoPath: String? = null
    private var customGridCols: Int = 0
    private var photoPaths: ArrayList<String>? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_difficulty_select)
        
        // 获取传入的参数
        selectedTheme = intent.getSerializableExtra("theme") as? GameTheme ?: GameTheme.CUTE
        selectedLevel = intent.getIntExtra("level", 1)
        imageUrl = intent.getStringExtra("imageUrl")

        // 语音搜图主题的额外参数
        searchQuery = intent.getStringExtra("search_query")
        imageUrls = intent.getStringArrayListExtra("image_urls")
        useDynamicGrid = intent.getBooleanExtra("useDynamicGrid", false)

        // 拍照主题的额外参数
        customPhotoPath = intent.getStringExtra("custom_photo_path")
        customGridCols = intent.getIntExtra("custom_grid_cols", 0)
        photoPaths = intent.getStringArrayListExtra("photo_paths")
        
        initializeViews()
        setupRecyclerView()
        setupClickListeners()
    }
    
    private fun initializeViews() {
        titleText = findViewById(R.id.tv_title)
        difficultyRecyclerView = findViewById(R.id.rv_difficulty)
        backButton = findViewById(R.id.btn_back)
        
        // 设置标题
        titleText.text = "选择难度等级"
    }
    
    private fun setupRecyclerView() {
        val difficultyAdapter = DifficultyAdapter(DifficultyLevel.getAllLevels()) { difficulty ->
            startPuzzleGame(difficulty)
        }
        
        difficultyRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@DifficultySelectActivity)
            adapter = difficultyAdapter
        }
    }
    
    private fun setupClickListeners() {
        backButton.setOnClickListener {
            finish()
        }
    }
    
    private fun startPuzzleGame(difficulty: DifficultyLevel) {
        val intent = Intent(this, PuzzleGameActivity::class.java).apply {
            putExtra("theme", selectedTheme.name)
            putExtra("level", selectedLevel)
            putExtra("difficulty", difficulty.name)

            // 传递基本参数
            imageUrl?.let { putExtra("imageUrl", it) }

            // 传递语音搜图主题的参数
            searchQuery?.let { putExtra("search_query", it) }
            imageUrls?.let { putStringArrayListExtra("image_urls", it) }
            putExtra("useDynamicGrid", useDynamicGrid)

            // 传递拍照主题的参数
            customPhotoPath?.let { putExtra("custom_photo_path", it) }
            putExtra("custom_grid_cols", customGridCols)
            photoPaths?.let { putStringArrayListExtra("photo_paths", it) }
        }
        startActivity(intent)
        finish()
    }
}

/**
 * 难度选择适配器
 */
class DifficultyAdapter(
    private val difficulties: List<DifficultyLevel>,
    private val onDifficultySelected: (DifficultyLevel) -> Unit
) : RecyclerView.Adapter<DifficultyAdapter.DifficultyViewHolder>() {
    
    class DifficultyViewHolder(itemView: android.view.View) : RecyclerView.ViewHolder(itemView) {
        val nameText: TextView = itemView.findViewById(R.id.tv_difficulty_name)
        val descriptionText: TextView = itemView.findViewById(R.id.tv_difficulty_description)
        val gridInfoText: TextView = itemView.findViewById(R.id.tv_grid_info)
        val selectButton: Button = itemView.findViewById(R.id.btn_select_difficulty)
    }
    
    override fun onCreateViewHolder(parent: android.view.ViewGroup, viewType: Int): DifficultyViewHolder {
        val view = android.view.LayoutInflater.from(parent.context)
            .inflate(R.layout.item_difficulty, parent, false)
        return DifficultyViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: DifficultyViewHolder, position: Int) {
        val difficulty = difficulties[position]
        
        holder.nameText.text = difficulty.displayName
        holder.descriptionText.text = difficulty.description
        
        // 显示网格信息
        val baseSize = difficulty.baseGridSize
        val totalPieces = baseSize * baseSize
        holder.gridInfoText.text = "基础网格: ${baseSize}×${baseSize} (${totalPieces}块)\n" +
                "实际网格会根据图片比例自动调整"
        
        // 设置难度颜色
        val colorRes = when (difficulty) {
            DifficultyLevel.EASY -> android.graphics.Color.GREEN
            DifficultyLevel.MEDIUM -> android.graphics.Color.BLUE
            DifficultyLevel.HARD -> android.graphics.Color.RED
        }
        holder.nameText.setTextColor(colorRes)
        
        holder.selectButton.setOnClickListener {
            onDifficultySelected(difficulty)
        }
        
        // 设置点击整个item也可以选择
        holder.itemView.setOnClickListener {
            onDifficultySelected(difficulty)
        }
    }
    
    override fun getItemCount(): Int = difficulties.size
}
