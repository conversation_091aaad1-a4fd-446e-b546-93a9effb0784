package com.ai.game_2

data class GameLevel(
    val levelNumber: Int,
    val gridSize: Int,
    val imageResourceId: Int,
    val title: String,
    val theme: GameTheme,
    val isUnlocked: Boolean = false,
    val imageUrl: String? = null // 支持网络图片URL
)

enum class GameTheme(val displayName: String, val emoji: String) {
    CUTE("可爱主题", "🥰"),
    CUSTOM_PHOTO("拍照拼图", "📸"),
    VOICE_SEARCH("语音搜图", "🎤")
}

object GameLevelManager {
    fun getAllThemes(): List<GameTheme> {
        return GameTheme.values().toList()
    }

    fun getLevelsForTheme(theme: GameTheme): List<GameLevel> {
        return when (theme) {
            GameTheme.CUTE -> getCuteLevels()
            GameTheme.CUSTOM_PHOTO -> getCustomPhotoLevels()
            GameTheme.VOICE_SEARCH -> getVoiceSearchLevels()
        }
    }

    private fun getCuteLevels(): List<GameLevel> {
        val cuteThemes = listOf(
            "可爱小猫 🐱", "萌萌小狗 🐶", "彩虹独角兽 🦄", "粉色小兔 🐰", "甜甜花朵 🌸",
            "星星月亮 ⭐", "爱心气球 💖", "蝴蝶花园 🦋", "彩虹糖果 🍭", "梦幻城堡 🏰",
            "小熊维尼 🐻", "可爱企鹅 🐧", "粉色火烈鸟 🦩", "小象宝宝 🐘", "萌萌考拉 🐨",
            "彩虹小马 🐴", "甜甜草莓 🍓", "可爱樱桃 🍒", "粉色桃子 🍑", "甜甜蛋糕 🎂",
            "彩虹冰淇淋 🍦", "可爱甜甜圈 🍩", "粉色棉花糖 🍭", "甜甜马卡龙 🧁", "彩虹棒棒糖 🍭",
            "小公主 👸", "可爱天使 👼", "粉色芭蕾 🩰", "甜甜皇冠 👑", "彩虹裙子 👗",
            "可爱太阳 ☀️", "粉色云朵 ☁️", "彩虹彩虹 🌈", "甜甜星星 ⭐", "可爱月亮 🌙",
            "小花仙子 🧚", "可爱精灵 🧝", "粉色魔法 ✨", "甜甜魔棒 🪄", "彩虹水晶 💎",
            "可爱小鸟 🐦", "粉色天鹅 🦢", "甜甜蜜蜂 🐝", "彩虹蝴蝶 🦋", "可爱瓢虫 🐞",
            "小美人鱼 🧜", "可爱海豚 🐬", "粉色贝壳 🐚", "甜甜珍珠 🦪", "彩虹鱼儿 🐠",
            "可爱小树 🌳", "粉色花朵 🌸", "甜甜向日葵 🌻", "彩虹郁金香 🌷", "可爱玫瑰 🌹",
            "小仙女屋 🏠", "可爱城堡 🏰", "粉色教堂 ⛪", "甜甜摩天轮 🎡", "彩虹滑梯 🎢",
            "可爱泰迪熊 🧸", "粉色洋娃娃 🪆", "甜甜积木 🧱", "彩虹气球 🎈", "可爱风车 🎪",
            "小音符 🎵", "可爱钢琴 🎹", "粉色吉他 🎸", "甜甜竖琴 🪕", "彩虹鼓 🥁",
            "可爱画笔 🖌️", "粉色调色板 🎨", "甜甜彩虹笔 🖍️", "彩虹画板 🎭", "可爱相机 📷",
            "小礼物 🎁", "可爱蝴蝶结 🎀", "粉色丝带 🎗️", "甜甜花环 💐", "彩虹花束 💝",
            "可爱小船 ⛵", "粉色热气球 🎈", "甜甜飞机 ✈️", "彩虹火车 🚂", "可爱汽车 🚗",
            "小雪花 ❄️", "可爱雪人 ⛄", "粉色圣诞树 🎄", "甜甜礼物盒 🎁", "彩虹铃铛 🔔",
            "可爱小鹿 🦌", "粉色小羊 🐑", "甜甜小猪 🐷", "彩虹小牛 🐄", "可爱小鸡 🐣",
            "小王子 🤴", "可爱骑士 🏇", "粉色马车 🎠", "甜甜宝剑 ⚔️", "彩虹盾牌 🛡️"
        )

        return (1..100).map { levelNumber ->
            val themeIndex = (levelNumber - 1) % cuteThemes.size
            GameLevel(
                levelNumber = levelNumber,
                gridSize = 3,
                imageResourceId = 0,
                title = cuteThemes[themeIndex],
                theme = GameTheme.CUTE,
                isUnlocked = levelNumber == 1 // 只有第一关默认解锁
            )
        }
    }

    private fun getCustomPhotoLevels(): List<GameLevel> {
        return listOf(
            GameLevel(1, 3, R.drawable.ic_camera_placeholder, "拍照拼图 📸", GameTheme.CUSTOM_PHOTO, true)
        )
    }

    private fun getVoiceSearchLevels(): List<GameLevel> {
        return listOf(
            GameLevel(1, 3, R.drawable.ic_voice_placeholder, "语音搜图 1 🎤", GameTheme.VOICE_SEARCH, true),
            GameLevel(2, 3, R.drawable.ic_voice_placeholder, "语音搜图 2 🔍", GameTheme.VOICE_SEARCH, false),
            GameLevel(3, 3, R.drawable.ic_voice_placeholder, "语音搜图 3 🖼️", GameTheme.VOICE_SEARCH, false),
            GameLevel(4, 3, R.drawable.ic_voice_placeholder, "语音搜图 4 🌟", GameTheme.VOICE_SEARCH, false),
            GameLevel(5, 3, R.drawable.ic_voice_placeholder, "语音搜图 5 🎨", GameTheme.VOICE_SEARCH, false),
            GameLevel(6, 3, R.drawable.ic_voice_placeholder, "语音搜图 6 🌈", GameTheme.VOICE_SEARCH, false),
            GameLevel(7, 3, R.drawable.ic_voice_placeholder, "语音搜图 7 ⭐", GameTheme.VOICE_SEARCH, false),
            GameLevel(8, 3, R.drawable.ic_voice_placeholder, "语音搜图 8 🎯", GameTheme.VOICE_SEARCH, false),
            GameLevel(9, 3, R.drawable.ic_voice_placeholder, "语音搜图 9 🚀", GameTheme.VOICE_SEARCH, false),
            GameLevel(10, 3, R.drawable.ic_voice_placeholder, "语音搜图 10 🏆", GameTheme.VOICE_SEARCH, false)
        )
    }

    fun getDifficultyText(gridSize: Int): String {
        return when (gridSize) {
            3 -> "超简单 ⭐"
            4 -> "有点难 ⭐⭐"
            5 -> "好难哦 ⭐⭐⭐"
            6 -> "超级难 ⭐⭐⭐⭐"
            7 -> "最难的 ⭐⭐⭐⭐⭐"
            else -> "特别的"
        }
    }

    fun getPieceCount(gridSize: Int): Int {
        return gridSize * gridSize
    }
}
