package com.ai.game_2

import android.graphics.Bitmap
import org.junit.Test
import org.junit.Assert.*
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

@RunWith(RobolectricTestRunner::class)
class PuzzleGameTest {

    private fun createTestBitmap(): Bitmap {
        return Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888)
    }

    @Test
    fun testGameLevelCreation() {
        val spongeBobLevels = GameLevelManager.getLevelsForTheme(GameTheme.SPONGEBOB)
        val labradorLevels = GameLevelManager.getLevelsForTheme(GameTheme.LABRADOR)

        // 验证每个主题有10个关卡
        assertEquals(10, spongeBobLevels.size)
        assertEquals(10, labradorLevels.size)

        // 验证所有关卡都是3x3网格（9块）
        spongeBobLevels.forEach { level ->
            assertEquals(3, level.gridSize)
        }
        labradorLevels.forEach { level ->
            assertEquals(3, level.gridSize)
        }

        // 验证第一关是解锁的
        assertTrue(spongeBobLevels[0].isUnlocked)
        assertTrue(labradorLevels[0].isUnlocked)

        // 验证其他关卡默认是锁定的
        assertFalse(spongeBobLevels[1].isUnlocked)
        assertFalse(labradorLevels[1].isUnlocked)
    }

    @Test
    fun testPuzzlePiecePosition() {
        // 创建一个测试拼图块
        val piece = PuzzlePiece(
            id = 0,
            correctRow = 1,
            correctCol = 1,
            currentRow = 1,
            currentCol = 1,
            bitmap = createTestBitmap()
        )

        // 验证初始位置是正确的
        assertTrue(piece.isInCorrectPosition())

        // 移动拼图块
        piece.currentRow = 0
        piece.currentCol = 0

        // 验证位置不正确
        assertFalse(piece.isInCorrectPosition())
    }

    @Test
    fun testPuzzlePieceSwap() {
        val piece1 = PuzzlePiece(
            id = 0,
            correctRow = 0,
            correctCol = 0,
            currentRow = 0,
            currentCol = 0,
            bitmap = createTestBitmap()
        )

        val piece2 = PuzzlePiece(
            id = 1,
            correctRow = 0,
            correctCol = 1,
            currentRow = 1,
            currentCol = 1,
            bitmap = createTestBitmap()
        )
        
        // 交换前的位置
        assertEquals(0, piece1.currentRow)
        assertEquals(0, piece1.currentCol)
        assertEquals(1, piece2.currentRow)
        assertEquals(1, piece2.currentCol)
        
        // 执行交换
        piece1.swapPosition(piece2)
        
        // 验证交换后的位置
        assertEquals(1, piece1.currentRow)
        assertEquals(1, piece1.currentCol)
        assertEquals(0, piece2.currentRow)
        assertEquals(0, piece2.currentCol)
    }

    @Test
    fun testDifficultyLevels() {
        assertEquals("超简单 ⭐", GameLevelManager.getDifficultyText(3))
        assertEquals("有点难 ⭐⭐", GameLevelManager.getDifficultyText(4))
        assertEquals("好难哦 ⭐⭐⭐", GameLevelManager.getDifficultyText(5))
        assertEquals("超级难 ⭐⭐⭐⭐", GameLevelManager.getDifficultyText(6))
        assertEquals("最难的 ⭐⭐⭐⭐⭐", GameLevelManager.getDifficultyText(7))
        assertEquals("特别的", GameLevelManager.getDifficultyText(8))
    }

    @Test
    fun testPieceCount() {
        assertEquals(9, GameLevelManager.getPieceCount(3))
        assertEquals(16, GameLevelManager.getPieceCount(4))
        assertEquals(25, GameLevelManager.getPieceCount(5))
        assertEquals(36, GameLevelManager.getPieceCount(6))
        assertEquals(49, GameLevelManager.getPieceCount(7))
    }

    @Test
    fun testThemes() {
        val themes = GameLevelManager.getAllThemes()
        assertEquals(4, themes.size)
        assertEquals(GameTheme.SPONGEBOB, themes[0])
        assertEquals(GameTheme.LABRADOR, themes[1])
        assertEquals(GameTheme.CUSTOM_PHOTO, themes[2])
        assertEquals(GameTheme.VOICE_SEARCH, themes[3])
    }

    @Test
    fun testCustomPhotoTheme() {
        val customLevels = GameLevelManager.getLevelsForTheme(GameTheme.CUSTOM_PHOTO)
        assertEquals(1, customLevels.size)
        assertTrue(customLevels[0].isUnlocked)
    }

    @Test
    fun testVoiceSearchTheme() {
        val voiceLevels = GameLevelManager.getLevelsForTheme(GameTheme.VOICE_SEARCH)
        assertEquals(10, voiceLevels.size)
        assertTrue(voiceLevels[0].isUnlocked)
        assertFalse(voiceLevels[1].isUnlocked)

        // 验证所有关卡都是3x3网格（9块）
        voiceLevels.forEach { level ->
            assertEquals(3, level.gridSize)
        }
    }

    @Test
    fun testPuzzleCompletionLogic() {
        // 创建一个简单的2x2拼图来测试完成逻辑
        val pieces = listOf(
            PuzzlePiece(0, 0, 0, 0, 0, createTestBitmap()),
            PuzzlePiece(1, 0, 1, 0, 1, createTestBitmap()),
            PuzzlePiece(2, 1, 0, 1, 0, createTestBitmap()),
            PuzzlePiece(3, 1, 1, 1, 1, createTestBitmap())
        )

        // 所有拼图块都在正确位置，应该完成
        assertTrue(pieces.all { it.isInCorrectPosition() })

        // 交换两个拼图块的位置
        pieces[0].swapPosition(pieces[1])

        // 现在应该不完成
        assertFalse(pieces.all { it.isInCorrectPosition() })

        // 交换回来
        pieces[0].swapPosition(pieces[1])

        // 又应该完成了
        assertTrue(pieces.all { it.isInCorrectPosition() })
    }
}
