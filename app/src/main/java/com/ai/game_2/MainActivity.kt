package com.ai.game_2

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity

class MainActivity : AppCompatActivity() {

    private lateinit var currentDifficultyText: TextView
    private lateinit var changeDifficultyButton: Button
    private lateinit var levelSelectButton: Button
    private lateinit var exitButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 初始化全局难度管理器
        GlobalDifficultyManager.initialize(this)

        setupUI()
        updateDifficultyDisplay()
    }

    override fun onResume() {
        super.onResume()
        // 从难度选择页面返回时更新显示
        updateDifficultyDisplay()
    }

    private fun setupUI() {
        currentDifficultyText = findViewById(R.id.tv_current_difficulty)
        changeDifficultyButton = findViewById(R.id.btn_change_difficulty)
        levelSelectButton = findViewById(R.id.btn_level_select)
        exitButton = findViewById(R.id.btn_exit)

        changeDifficultyButton.setOnClickListener {
            openDifficultySelector()
        }

        levelSelectButton.setOnClickListener {
            val intent = Intent(this, ThemeSelectActivity::class.java)
            startActivity(intent)
        }

        exitButton.setOnClickListener {
            finish()
        }
    }

    private fun openDifficultySelector() {
        val intent = Intent(this, GlobalDifficultySelectActivity::class.java)
        startActivity(intent)
    }

    private fun updateDifficultyDisplay() {
        val currentDifficulty = GlobalDifficultyManager.getCurrentDifficulty()
        val baseSize = currentDifficulty.baseGridSize
        currentDifficultyText.text = "${currentDifficulty.displayName} (${baseSize}×${baseSize} 基础格子)"
    }
}
