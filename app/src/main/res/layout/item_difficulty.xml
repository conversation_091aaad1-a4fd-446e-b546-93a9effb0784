<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="8dp"
    android:layout_marginHorizontal="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 难度名称 -->
        <TextView
            android:id="@+id/tv_difficulty_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="中等"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text_color"
            android:layout_marginBottom="8dp" />

        <!-- 难度描述 -->
        <TextView
            android:id="@+id/tv_difficulty_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="3×3 格子，适中难度"
            android:textSize="16sp"
            android:textColor="@color/secondary_text_color"
            android:layout_marginBottom="12dp" />

        <!-- 网格信息 -->
        <TextView
            android:id="@+id/tv_grid_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="基础网格: 3×3 (9块)\n实际网格会根据图片比例自动调整"
            android:textSize="14sp"
            android:textColor="@color/hint_text_color"
            android:background="@color/light_gray"
            android:padding="12dp"
            android:layout_marginBottom="16dp" />

        <!-- 选择按钮 -->
        <Button
            android:id="@+id/btn_select_difficulty"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="选择此难度"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/button_primary"
            android:textColor="@color/button_text_color"
            android:layout_gravity="center" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
