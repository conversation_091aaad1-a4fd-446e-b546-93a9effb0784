package com.ai.game_2

import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity

class TestActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        try {
            // 创建一个简单的布局
            setContentView(createSimpleLayout())
            
            Toast.makeText(this, "测试Activity启动成功！", Toast.LENGTH_SHORT).show()
            
        } catch (e: Exception) {
            Toast.makeText(this, "错误: ${e.message}", Toast.LENGTH_LONG).show()
            e.printStackTrace()
        }
    }
    
    private fun createSimpleLayout(): Int {
        // 使用现有的主界面布局
        return R.layout.activity_main
    }
}
