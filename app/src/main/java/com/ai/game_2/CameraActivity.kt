package com.ai.game_2

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.widget.*
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

class CameraActivity : AppCompatActivity() {
    
    private lateinit var imageView: ImageView
    private lateinit var takePhotoButton: Button
    private lateinit var usePhotoButton: Button
    private lateinit var retakeButton: Button
    private lateinit var backButton: Button
    private lateinit var gridSizeSpinner: Spinner
    private lateinit var gridInfoText: TextView

    private var currentPhotoPath: String? = null
    private var capturedBitmap: Bitmap? = null
    private var selectedLevel: Int = 1
    private var selectedGridCols: Int = 3  // 用户选择的横格数
    
    companion object {
        private const val REQUEST_IMAGE_CAPTURE = 1
        private const val REQUEST_CAMERA_PERMISSION = 2
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_camera)
        
        selectedLevel = intent.getIntExtra("level", 1)
        
        initializeViews()
        setupClickListeners()
        checkCameraPermission()
    }
    
    private fun initializeViews() {
        imageView = findViewById(R.id.iv_photo_preview)
        takePhotoButton = findViewById(R.id.btn_take_photo)
        usePhotoButton = findViewById(R.id.btn_use_photo)
        retakeButton = findViewById(R.id.btn_retake)
        backButton = findViewById(R.id.btn_back)
        gridSizeSpinner = findViewById(R.id.spinner_grid_size)
        gridInfoText = findViewById(R.id.tv_grid_info)

        setupGridSizeSpinner()

        // 初始状态
        usePhotoButton.isEnabled = false
        retakeButton.isEnabled = false
    }
    
    private fun setupClickListeners() {
        takePhotoButton.setOnClickListener {
            if (checkCameraPermission()) {
                dispatchTakePictureIntent()
            }
        }
        
        usePhotoButton.setOnClickListener {
            capturedBitmap?.let { bitmap ->
                savePhotoAndStartGame(bitmap)
            }
        }
        
        retakeButton.setOnClickListener {
            resetCamera()
        }
        
        backButton.setOnClickListener {
            finish()
        }
    }

    private fun setupGridSizeSpinner() {
        val gridOptions = arrayOf("3格", "4格", "5格", "6格")
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, gridOptions)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        gridSizeSpinner.adapter = adapter

        // 默认选择3格
        gridSizeSpinner.setSelection(0)
        selectedGridCols = 3

        gridSizeSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                selectedGridCols = position + 3  // 3, 4, 5, 6
                updateGridInfo()
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        updateGridInfo()
    }

    private fun updateGridInfo() {
        if (capturedBitmap != null) {
            val calculatedRows = calculateRowsForCols(capturedBitmap!!.width, capturedBitmap!!.height, selectedGridCols)
            val totalPieces = selectedGridCols * calculatedRows
            gridInfoText.text = "拼图网格：${selectedGridCols}×${calculatedRows} (${totalPieces}块)"
        } else {
            gridInfoText.text = "横格：${selectedGridCols}格，纵格将根据照片比例自动计算"
        }
    }

    private fun calculateRowsForCols(width: Int, height: Int, cols: Int): Int {
        val aspectRatio = height.toFloat() / width.toFloat()
        val calculatedRows = (cols * aspectRatio).toInt()

        // 确保至少有2行，最多8行
        return calculatedRows.coerceIn(2, 8)
    }
    
    private fun checkCameraPermission(): Boolean {
        return if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) 
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, 
                arrayOf(Manifest.permission.CAMERA), 
                REQUEST_CAMERA_PERMISSION)
            false
        } else {
            true
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            REQUEST_CAMERA_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(this, "相机权限已获取", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "需要相机权限才能拍照", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
    
    private fun dispatchTakePictureIntent() {
        Intent(MediaStore.ACTION_IMAGE_CAPTURE).also { takePictureIntent ->
            takePictureIntent.resolveActivity(packageManager)?.also {
                val photoFile: File? = try {
                    createImageFile()
                } catch (ex: IOException) {
                    Toast.makeText(this, "创建图片文件失败", Toast.LENGTH_SHORT).show()
                    null
                }
                
                photoFile?.also {
                    val photoURI: Uri = FileProvider.getUriForFile(
                        this,
                        "com.ai.game_2.fileprovider",
                        it
                    )
                    takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                    startActivityForResult(takePictureIntent, REQUEST_IMAGE_CAPTURE)
                }
            }
        }
    }
    
    @Throws(IOException::class)
    private fun createImageFile(): File {
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir: File? = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "PUZZLE_${timeStamp}_",
            ".jpg",
            storageDir
        ).apply {
            currentPhotoPath = absolutePath
        }
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_IMAGE_CAPTURE && resultCode == RESULT_OK) {
            currentPhotoPath?.let { path ->
                val bitmap = BitmapFactory.decodeFile(path)
                bitmap?.let {
                    capturedBitmap = it
                    imageView.setImageBitmap(it)

                    // 更新按钮状态
                    usePhotoButton.isEnabled = true
                    retakeButton.isEnabled = true
                    takePhotoButton.text = "重新拍照"

                    // 更新网格信息
                    updateGridInfo()
                }
            }
        }
    }
    
    private fun resetCamera() {
        imageView.setImageResource(R.drawable.ic_camera_placeholder)
        capturedBitmap = null
        currentPhotoPath = null

        // 重置按钮状态
        usePhotoButton.isEnabled = false
        retakeButton.isEnabled = false
        takePhotoButton.text = "拍照 📸"

        // 重置网格信息
        updateGridInfo()
    }
    
    private fun savePhotoAndStartGame(bitmap: Bitmap) {
        try {
            // 保存图片到内部存储
            val filename = "custom_photo_level_${selectedLevel}.jpg"
            val file = File(filesDir, filename)
            
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
            }
            
            // 计算纵格数
            val calculatedRows = calculateRowsForCols(bitmap.width, bitmap.height, selectedGridCols)

            // 启动拼图游戏
            val intent = Intent(this, PuzzleGameActivity::class.java)
            intent.putExtra("theme", GameTheme.CUSTOM_PHOTO.name)
            intent.putExtra("level", selectedLevel)
            intent.putExtra("custom_photo_path", file.absolutePath)
            intent.putExtra("custom_grid_cols", selectedGridCols)
            intent.putExtra("custom_grid_rows", calculatedRows)
            startActivity(intent)
            finish()
            
        } catch (e: Exception) {
            Toast.makeText(this, "保存图片失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
}
