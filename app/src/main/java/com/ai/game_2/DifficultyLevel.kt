package com.ai.game_2

/**
 * 拼图难度等级
 */
enum class DifficultyLevel(
    val displayName: String,
    val baseGridSize: Int,
    val description: String
) {
    EASY("简单", 2, "2×2 格子，适合初学者"),
    MEDIUM("中等", 3, "3×3 格子，适中难度"),
    HARD("困难", 4, "4×4 格子，挑战高手");
    
    companion object {
        /**
         * 获取默认难度等级
         */
        fun getDefault(): DifficultyLevel = MEDIUM
        
        /**
         * 根据名称获取难度等级
         */
        fun fromName(name: String): DifficultyLevel {
            return values().find { it.name == name } ?: getDefault()
        }
        
        /**
         * 获取所有难度等级列表
         */
        fun getAllLevels(): List<DifficultyLevel> = values().toList()
    }
}

/**
 * 难度管理器 - 处理网格计算逻辑
 */
object DifficultyManager {
    
    /**
     * 根据难度等级和图片比例计算网格尺寸
     * @param difficulty 难度等级
     * @param imageWidth 图片宽度
     * @param imageHeight 图片高度
     * @return Pair<列数, 行数>
     */
    fun calculateGridSize(difficulty: DifficultyLevel, imageWidth: Int, imageHeight: Int): Pair<Int, Int> {
        val baseSize = difficulty.baseGridSize
        
        // 计算图片的宽高比
        val aspectRatio = imageWidth.toFloat() / imageHeight.toFloat()
        
        return when {
            // 正方形图片或接近正方形 (比例在0.8-1.25之间)
            aspectRatio >= 0.8f && aspectRatio <= 1.25f -> {
                Pair(baseSize, baseSize)
            }
            // 横向图片 (宽 > 高)
            aspectRatio > 1.25f -> {
                val cols = baseSize + 1 // 横向多一列
                val rows = baseSize
                Pair(cols, rows)
            }
            // 纵向图片 (高 > 宽)
            aspectRatio < 0.8f -> {
                val cols = baseSize
                val rows = baseSize + 1 // 纵向多一行
                Pair(cols, rows)
            }
            else -> {
                Pair(baseSize, baseSize)
            }
        }
    }
    
    /**
     * 根据难度等级计算正方形网格尺寸（用于固定比例的主题）
     */
    fun getSquareGridSize(difficulty: DifficultyLevel): Pair<Int, Int> {
        val size = difficulty.baseGridSize
        return Pair(size, size)
    }
    
    /**
     * 计算拼图块总数
     */
    fun getTotalPieces(cols: Int, rows: Int): Int = cols * rows
    
    /**
     * 获取难度描述文本
     */
    fun getDifficultyDescription(difficulty: DifficultyLevel, cols: Int, rows: Int): String {
        val totalPieces = getTotalPieces(cols, rows)
        return "${difficulty.displayName} ${cols}×${rows} (${totalPieces}块)"
    }
    
    /**
     * 根据拼图块数量推断难度等级
     */
    fun inferDifficultyFromPieces(totalPieces: Int): DifficultyLevel {
        return when {
            totalPieces <= 4 -> DifficultyLevel.EASY
            totalPieces <= 9 -> DifficultyLevel.MEDIUM
            else -> DifficultyLevel.HARD
        }
    }
    
    /**
     * 检查网格尺寸是否合理
     */
    fun isValidGridSize(cols: Int, rows: Int): Boolean {
        return cols >= 2 && rows >= 2 && cols <= 6 && rows <= 6
    }
    
    /**
     * 获取推荐的难度等级（基于用户经验）
     */
    fun getRecommendedDifficulty(completedLevels: Int): DifficultyLevel {
        return when {
            completedLevels < 5 -> DifficultyLevel.EASY
            completedLevels < 15 -> DifficultyLevel.MEDIUM
            else -> DifficultyLevel.HARD
        }
    }
}
