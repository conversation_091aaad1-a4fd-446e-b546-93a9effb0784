package com.ai.game_2

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import kotlinx.coroutines.*

class LevelAdapter(
    private var levels: List<GameLevel>,
    private val onLevelClick: (GameLevel) -> Unit
) : RecyclerView.Adapter<LevelAdapter.LevelViewHolder>() {
    
    class LevelViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val levelNumber: TextView = itemView.findViewById(R.id.tv_level_number)
        val levelTitle: TextView = itemView.findViewById(R.id.tv_level_title)
        val levelImage: ImageView = itemView.findViewById(R.id.iv_level_image)
        val lockIcon: ImageView = itemView.findViewById(R.id.iv_lock_icon)
        val pieceCount: TextView = itemView.findViewById(R.id.tv_piece_count)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LevelViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_level, parent, false)
        return LevelViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: LevelViewHolder, position: Int) {
        val level = levels[position]
        
        holder.levelNumber.text = "第${level.levelNumber}关"
        holder.levelTitle.text = level.title
        // 根据主题类型显示拼图块数量
        val pieceCountText = when (level.theme) {
            GameTheme.CUSTOM_PHOTO -> "智能分配"
            GameTheme.VOICE_SEARCH -> "9块"
            else -> "9块"  // 其他主题固定9块
        }
        holder.pieceCount.text = pieceCountText
        
        if (level.isUnlocked) {
            // 根据主题类型设置图片
            when (level.theme) {
                GameTheme.CUTE -> {
                    // 先显示占位符，然后异步加载可爱图片
                    holder.levelImage.setImageResource(R.drawable.ic_voice_placeholder)

                    // 异步生成图片，避免阻塞主线程
                    CoroutineScope(Dispatchers.Main).launch {
                        try {
                            val generator = CuteImageGenerator()
                            val cuteBitmap = generator.generateCuteImage(
                                level.levelNumber,
                                200,
                                200,
                                holder.itemView.context.cacheDir
                            )
                            if (cuteBitmap != null) {
                                holder.levelImage.setImageBitmap(cuteBitmap)
                                // 恢复正常状态
                                holder.levelImage.alpha = 1.0f
                                holder.levelTitle.alpha = 1.0f
                                holder.levelNumber.alpha = 1.0f
                                holder.itemView.isEnabled = true
                            } else {
                                // 生成失败，保持占位符
                                holder.levelImage.setImageResource(R.drawable.ic_voice_placeholder)
                            }
                        } catch (e: Exception) {
                            // 如果生成失败，保持占位符
                            holder.levelImage.setImageResource(R.drawable.ic_voice_placeholder)
                        }
                    }
                }
                GameTheme.VOICE_SEARCH -> {
                    // 语音搜图主题使用网络图片
                    android.util.Log.d("LevelAdapter", "语音搜图关卡 ${level.levelNumber}, imageUrl: ${level.imageUrl}")

                    if (!level.imageUrl.isNullOrEmpty() &&
                        !level.imageUrl.startsWith("default_image_") &&
                        !level.imageUrl.startsWith("voice_search_placeholder_")) {
                        // 真实的网络图片URL
                        android.util.Log.d("LevelAdapter", "加载真实图片: ${level.imageUrl}")
                        Glide.with(holder.itemView.context)
                            .load(level.imageUrl)
                            .placeholder(R.drawable.ic_voice_placeholder)
                            .error(R.drawable.ic_voice_placeholder)
                            .diskCacheStrategy(DiskCacheStrategy.DATA)
                            .timeout(10000)
                            .centerCrop()
                            .into(holder.levelImage)
                    } else {
                        // 默认图片或空URL
                        android.util.Log.d("LevelAdapter", "使用默认图片")
                        holder.levelImage.setImageResource(R.drawable.ic_voice_placeholder)
                    }
                }
                else -> {
                    // 其他主题使用资源图片
                    holder.levelImage.setImageResource(level.imageResourceId)
                }
            }
            holder.levelImage.alpha = 1.0f
            holder.lockIcon.visibility = View.GONE
            holder.itemView.isEnabled = true
            holder.itemView.alpha = 1.0f
        } else {
            holder.levelImage.setImageResource(R.drawable.ic_lock)
            holder.levelImage.alpha = 0.5f
            holder.lockIcon.visibility = View.VISIBLE
            holder.itemView.isEnabled = false
            holder.itemView.alpha = 0.5f
        }
        
        holder.itemView.setOnClickListener {
            if (level.isUnlocked) {
                onLevelClick(level)
            }
        }
    }
    
    override fun getItemCount(): Int = levels.size
    
    fun updateLevels(newLevels: List<GameLevel>) {
        levels = newLevels
        notifyDataSetChanged()
    }
}
