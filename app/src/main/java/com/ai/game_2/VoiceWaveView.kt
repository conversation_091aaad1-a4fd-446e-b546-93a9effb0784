package com.ai.game_2

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import kotlin.math.*
import kotlin.random.Random

class VoiceWaveView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val waveAmplitudes = mutableListOf<Float>()
    private val maxBars = 50
    private var isRecording = false
    private var animationTime = 0f
    
    init {
        paint.color = Color.parseColor("#FF6B9D")
        paint.strokeWidth = 8f
        paint.strokeCap = Paint.Cap.ROUND
        
        // 初始化波形数据
        repeat(maxBars) {
            waveAmplitudes.add(0.1f)
        }
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        val centerY = height / 2f
        val barWidth = width / maxBars.toFloat()
        
        waveAmplitudes.forEachIndexed { index, amplitude ->
            val x = index * barWidth + barWidth / 2
            val barHeight = amplitude * height * 0.4f
            
            // 绘制上半部分
            canvas.drawLine(
                x, centerY - barHeight / 2,
                x, centerY + barHeight / 2,
                paint
            )
        }
    }
    
    fun startRecording() {
        isRecording = true
        startAnimation()
    }
    
    fun stopRecording() {
        isRecording = false
        // 逐渐减小波形
        smoothToZero()
    }
    
    private fun startAnimation() {
        if (!isRecording) return
        
        // 更新波形数据
        updateWaveform()
        
        // 重绘
        invalidate()
        
        // 继续动画
        postDelayed({ startAnimation() }, 50) // 20fps
    }
    
    private fun updateWaveform() {
        animationTime += 0.1f
        
        for (i in waveAmplitudes.indices) {
            if (isRecording) {
                // 模拟语音输入的波形
                val baseAmplitude = 0.3f + Random.nextFloat() * 0.7f
                val wave = sin(animationTime + i * 0.2f) * 0.3f
                waveAmplitudes[i] = baseAmplitude + wave
            }
        }
    }
    
    private fun smoothToZero() {
        for (i in waveAmplitudes.indices) {
            waveAmplitudes[i] *= 0.9f
        }
        
        invalidate()
        
        // 如果还有明显的波形，继续减小
        if (waveAmplitudes.any { it > 0.05f }) {
            postDelayed({ smoothToZero() }, 50)
        }
    }
    
    fun setWaveColor(color: Int) {
        paint.color = color
        invalidate()
    }
}
