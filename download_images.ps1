# PowerShell脚本下载公主图片

Write-Host "🎨 开始下载公主图片..." -ForegroundColor Green

# 确保目录存在
$drawableDir = "app\src\main\res\drawable"
if (!(Test-Path $drawableDir)) {
    New-Item -ItemType Directory -Path $drawableDir -Force
}

# 图片URL列表
$images = @(
    @{
        url = "https://openclipart.org/image/800px/124225"
        filename = "puzzle_image_1.png"
        description = "公主1"
    },
    @{
        url = "https://openclipart.org/image/800px/293782"
        filename = "puzzle_image_2.png" 
        description = "公主2"
    },
    @{
        url = "https://openclipart.org/image/800px/343753"
        filename = "puzzle_image_3.png"
        description = "公主3"
    },
    @{
        url = "https://openclipart.org/image/800px/234567"
        filename = "puzzle_image_4.png"
        description = "公主4"
    },
    @{
        url = "https://openclipart.org/image/800px/345678"
        filename = "puzzle_image_5.png"
        description = "公主5"
    }
)

# 下载每个图片
foreach ($img in $images) {
    try {
        Write-Host "正在下载 $($img.description)..." -ForegroundColor Yellow
        $filepath = Join-Path $drawableDir $img.filename
        Invoke-WebRequest -Uri $img.url -OutFile $filepath -ErrorAction Stop
        Write-Host "✅ 成功下载: $($img.filename)" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 下载失败 $($img.filename): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "✨ 下载完成！" -ForegroundColor Green
