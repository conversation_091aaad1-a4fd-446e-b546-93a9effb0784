package com.ai.game_2

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView

class ThemeSelectActivity : AppCompatActivity() {
    
    private lateinit var recyclerView: RecyclerView
    private lateinit var backButton: Button
    private lateinit var themeAdapter: ThemeAdapter
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_theme_select)
        
        initializeViews()
        setupRecyclerView()
        setupClickListeners()
    }
    
    private fun initializeViews() {
        recyclerView = findViewById(R.id.rv_themes)
        backButton = findViewById(R.id.btn_back)
    }
    
    private fun setupRecyclerView() {
        recyclerView.layoutManager = GridLayoutManager(this, 1)
        themeAdapter = ThemeAdapter(GameLevelManager.getAllThemes()) { theme ->
            openLevelSelect(theme)
        }
        recyclerView.adapter = themeAdapter
    }
    
    private fun setupClickListeners() {
        backButton.setOnClickListener {
            finish()
        }
    }
    
    private fun openLevelSelect(theme: GameTheme) {
        when (theme) {
            GameTheme.VOICE_SEARCH -> {
                // 语音搜图主题：先进入语音搜索界面
                val intent = Intent(this, VoiceSearchActivity::class.java)
                startActivity(intent)
            }
            else -> {
                // 其他主题：直接进入关卡选择界面
                val intent = Intent(this, LevelSelectActivity::class.java)
                intent.putExtra("theme", theme.name)
                startActivity(intent)
            }
        }
    }
}
