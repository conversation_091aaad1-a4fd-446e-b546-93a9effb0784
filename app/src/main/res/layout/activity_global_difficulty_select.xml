<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color"
    android:fitsSystemWindows="true">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/header_background"
        android:gravity="center_vertical">

        <Button
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/ic_back"
            android:layout_marginEnd="16dp" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🎯 设置全局游戏难度"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text_color"
            android:gravity="center" />

        <!-- 占位符，保持标题居中 -->
        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />

    </LinearLayout>

    <!-- 说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🎮 选择适合你的全局拼图难度\n📐 所有拼图的网格大小都会根据此设置和图片比例自动调整"
        android:textSize="14sp"
        android:textColor="@color/secondary_text_color"
        android:gravity="center"
        android:padding="16dp"
        android:background="@color/light_background" />

    <!-- 当前设置统计 -->
    <TextView
        android:id="@+id/tv_current_stats"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="📊 当前设置效果：\n加载中..."
        android:textSize="12sp"
        android:textColor="@color/hint_text_color"
        android:padding="16dp"
        android:background="@color/light_gray"
        android:layout_margin="16dp" />

    <!-- 难度选择列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_difficulty"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp"
        android:clipToPadding="false" />

    <!-- 底部提示 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="💡 提示：此设置将应用于所有拼图主题和关卡"
        android:textSize="14sp"
        android:textColor="@color/secondary_text_color"
        android:gravity="center"
        android:padding="16dp"
        android:background="@color/footer_background" />

</LinearLayout>
