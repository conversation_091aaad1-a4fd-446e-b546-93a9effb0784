# 儿童拼图乐园 🧩

一个专为儿童设计的Android拼图游戏应用，支持多种主题和智能图片搜索功能。

## 📱 应用特色

### 🎨 三大主题模式
- **可爱主题** 🥰 - 精选儿童喜爱的卡通图片
- **拍照拼图** 📸 - 使用相机拍摄照片制作个性化拼图
- **语音搜图** 🎤 - 语音输入关键词，智能搜索相关图片

### 🌟 核心功能
- **智能图片搜索** - 集成百度图片搜索API，支持中文关键词
- **语音识别** - 支持语音输入搜索关键词
- **多级难度** - 每个主题包含10个关卡，难度递增
- **拖拽拼图** - 直观的拖拽操作，适合儿童使用
- **完成动画** - 拼图完成后的庆祝动画效果

## 🛠️ 技术架构

### 开发环境
- **语言**: Kotlin
- **平台**: Android (API 24+)
- **架构**: MVVM + Repository Pattern
- **UI框架**: Material Design

### 核心依赖
```kotlin
// 网络请求
implementation("com.squareup.okhttp3:okhttp:4.10.0")

// 图片加载
implementation("com.github.bumptech.glide:glide:4.14.2")

// 协程
implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4")

// UI组件
implementation("androidx.recyclerview:recyclerview:1.3.2")
implementation("androidx.cardview:cardview:1.0.0")
```

## 📁 项目结构

```
app/src/main/java/com/ai/puzzlegame/
├── MainActivity.kt                 # 主界面
├── GameLevel.kt                   # 游戏关卡和主题定义
├── ImageSearchService.kt          # 图片搜索服务
├── ThemeSelectActivity.kt         # 主题选择界面
├── LevelSelectActivity.kt         # 关卡选择界面
├── PuzzleGameActivity.kt          # 普通拼图游戏
├── VoiceSearchActivity.kt         # 语音搜索界面
├── VoicePuzzleActivity.kt         # 语音拼图游戏
├── CameraPuzzleActivity.kt        # 拍照拼图
├── ThemeAdapter.kt                # 主题列表适配器
├── LevelAdapter.kt                # 关卡列表适配器
├── DownloadedImageAdapter.kt      # 下载图片适配器
├── PuzzlePieceAdapter.kt          # 拼图块适配器
└── PuzzlePiece.kt                 # 拼图块数据类
```

## 🚀 快速开始

### 环境要求
- Android Studio Arctic Fox 或更高版本
- Android SDK API 24 或更高版本
- Kotlin 1.8.0 或更高版本

### 安装步骤
1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd game_2
   ```

2. **打开项目**
   - 使用Android Studio打开项目
   - 等待Gradle同步完成

3. **运行应用**
   - 连接Android设备或启动模拟器
   - 点击运行按钮或按Ctrl+F5

## 🎮 使用指南

### 可爱主题 🥰
1. 选择"可爱主题"
2. 选择关卡（1-10关）
3. 拖拽拼图块到正确位置
4. 完成拼图解锁下一关

### 拍照拼图 📸
1. 选择"拍照拼图"
2. 点击"拍照"按钮
3. 使用相机拍摄照片
4. 开始拼图游戏

### 语音搜图 🎤
1. 选择"语音搜图"
2. 点击语音按钮或直接输入关键词
3. 等待图片搜索和下载
4. 开始10关拼图挑战

## 🔧 配置说明

### 网络权限
应用需要以下权限：
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### 网络安全配置
支持HTTP和HTTPS请求，配置文件：`app/src/main/res/xml/network_security_config.xml`

## 🌐 图片搜索功能

### 百度图片搜索
- **API端点**: `https://image.baidu.com/search/acjson`
- **支持中文关键词**
- **自动解析JSON响应**
- **多层备用机制**

### 备用图片源
- Unsplash (source.unsplash.com)
- LoremFlickr (loremflickr.com)
- Picsum (picsum.photos)

### 搜索流程
1. 用户输入关键词
2. 调用百度图片搜索API
3. 解析返回的JSON数据
4. 提取图片URL列表
5. 如果失败，使用备用图片源
6. 下载图片并创建拼图

## 🎯 游戏特色

### 智能难度调节
- **3x3网格** - 适合初学者
- **自适应网格** - 拍照模式根据图片比例调整
- **渐进式解锁** - 完成当前关卡解锁下一关

### 用户体验优化
- **加载提示** - 图片下载进度显示
- **错误处理** - 网络异常时的友好提示
- **缓存机制** - 减少重复下载
- **离线支持** - 本地图片资源备用

## 🐛 故障排除

### 常见问题

**Q: 语音搜图没有返回相关图片？**
A: 检查网络连接，确保能访问百度图片搜索。如果百度搜索失败，系统会自动使用备用图片源。

**Q: 拍照功能无法使用？**
A: 确保已授予相机权限，并且设备有可用的相机。

**Q: 应用启动崩溃？**
A: 检查Android版本是否支持（需要API 24+），清理应用缓存后重试。

### 调试信息
应用包含详细的日志输出，可在Android Studio的Logcat中查看：
```
过滤器: com.ai.puzzlegame
```

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发规范
- 使用Kotlin编程语言
- 遵循Material Design设计规范
- 添加适当的注释和文档
- 确保代码通过测试

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至：[<EMAIL>]

---

**儿童拼图乐园** - 让学习变得更有趣！🎉
