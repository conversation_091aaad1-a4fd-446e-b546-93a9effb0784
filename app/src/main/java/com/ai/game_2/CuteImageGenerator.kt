package com.ai.game_2

import android.content.Context
import android.graphics.*
import android.util.Log
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.cos
import kotlin.math.sin

class CuteImageGenerator(private val context: Context) {

    private val width = 400
    private val height = 400

    // 图片永久存储目录（使用内部存储，不会被清理）
    private val imageDir = File(context.filesDir, "cute_images")

    // 下载失败记录文件
    private val failedDownloadsFile = File(context.filesDir, "failed_downloads.txt")

    // 简笔画搜索关键词
    private val cuteThemes = listOf(
        "cat simple drawing outline",
        "car simple drawing outline",
        "dinosaur simple drawing outline",
        "castle simple drawing outline",
        "rocket simple drawing outline",
        "flower simple drawing outline",
        "robot simple drawing outline",
        "butterfly simple drawing outline",
        "house simple drawing outline",
        "unicorn simple drawing outline"
    )

    init {
        // 创建图片存储目录
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        }
    }

    // 免费图片网站API（无版权问题）
    private val freeImageSources = listOf(
        "https://pixabay.com/api/",
        "https://api.unsplash.com/search/photos",
        "https://api.pexels.com/v1/search"
    )

    // 可靠的免费简笔画图片URL（来自免费图片网站）
    private val reliableImageUrls = mapOf(
        1 to listOf( // 可爱小猫简笔画
            "https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1533738363-b7f9aef128ce?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1592194996308-7b43878e84a6?w=400&h=400&fit=crop"
        ),
        2 to listOf( // 可爱小汽车
            "https://images.unsplash.com/photo-1449965408869-eaa3f722e40d?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1502877338535-766e1452684a?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=400&h=400&fit=crop"
        ),
        3 to listOf( // 可爱恐龙
            "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop"
        ),
        4 to listOf( // 可爱城堡
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=400&fit=crop"
        ),
        5 to listOf( // 可爱火箭
            "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1516849841032-87cbac4d88f7?w=400&h=400&fit=crop"
        ),
        6 to listOf( // 可爱花朵
            "https://images.unsplash.com/photo-1490750967868-88aa4486c946?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=400&fit=crop"
        ),
        7 to listOf( // 可爱机器人
            "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop"
        ),
        8 to listOf( // 可爱蝴蝶
            "https://images.unsplash.com/photo-1444927714506-8492d94b5ba0?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop"
        ),
        9 to listOf( // 可爱房子
            "https://images.unsplash.com/photo-1518780664697-55e3ad937233?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop"
        ),
        10 to listOf( // 可爱独角兽
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop",
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop"
        )
    )
    
    // 检查关卡是否可用（下载是否成功）
    fun isLevelAvailable(level: Int): Boolean {
        // 检查是否有本地图片
        if (hasLocalImage(level)) {
            return true
        }

        // 检查是否在失败列表中
        return !isLevelFailed(level)
    }

    // 异步生成可爱图片
    suspend fun generateCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 检查关卡是否可用
                if (!isLevelAvailable(level)) {
                    return@withContext null // 关卡不可用，返回null
                }

                // 尝试从本地或网络获取图片
                downloadCuteImage(level)
            } catch (e: Exception) {
                // 下载失败，标记关卡为不可用
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 同步版本（保持兼容性）
    fun generateCuteImageSync(level: Int): Bitmap? {
        return runBlocking {
            generateCuteImage(level)
        }
    }
    
    // 从本地或网络获取简笔画图片
    private suspend fun downloadCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 首先检查本地是否已有图片
                val localBitmap = getLocalImage(level)
                if (localBitmap != null) {
                    return@withContext localBitmap // 直接返回本地图片，不重复下载
                }

                // 本地没有，从可靠的URL下载图片
                Log.d("CuteImageGenerator", "开始为关卡 $level 下载图片")
                val imageUrls = reliableImageUrls[level]
                if (imageUrls != null) {
                    for (imageUrl in imageUrls) {
                        try {
                            Log.d("CuteImageGenerator", "尝试下载: $imageUrl")
                            val bitmap = downloadAndSaveImage(imageUrl, level)
                            if (bitmap != null) {
                                Log.d("CuteImageGenerator", "关卡 $level 下载成功")
                                return@withContext resizeBitmap(bitmap, width, height)
                            } else {
                                Log.d("CuteImageGenerator", "下载失败: $imageUrl")
                            }
                        } catch (e: Exception) {
                            Log.e("CuteImageGenerator", "下载异常: $imageUrl", e)
                            continue
                        }
                    }
                }



                // 所有下载都失败，标记为失败
                markLevelAsFailed(level)
                null
            } catch (e: Exception) {
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 检查本地是否有图片
    private fun hasLocalImage(level: Int): Boolean {
        val imageFile = File(imageDir, "cute_level_$level.png")
        return imageFile.exists()
    }

    // 获取本地图片
    private fun getLocalImage(level: Int): Bitmap? {
        return try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            if (imageFile.exists()) {
                BitmapFactory.decodeFile(imageFile.absolutePath)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    // 下载图片并永久保存
    private suspend fun downloadAndSaveImage(imageUrl: String, level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val bitmap = downloadImageFromUrl(imageUrl)
                if (bitmap != null) {
                    // 永久保存到本地
                    saveBitmapToLocal(bitmap, level)
                    bitmap
                } else {
                    null
                }
            } catch (e: Exception) {
                null
            }
        }
    }

    // 永久保存图片到本地
    private fun saveBitmapToLocal(bitmap: Bitmap, level: Int) {
        try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            val outputStream = FileOutputStream(imageFile)
            bitmap.compress(Bitmap.CompressFormat.PNG, 95, outputStream) // 高质量保存
            outputStream.close()
        } catch (e: Exception) {
            // 保存失败不影响主要功能
        }
    }

    // 检查关卡是否下载失败
    private fun isLevelFailed(level: Int): Boolean {
        return try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",")
                failedLevels.contains(level.toString())
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }

    // 标记关卡为下载失败
    private fun markLevelAsFailed(level: Int) {
        try {
            val failedLevels = if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText().split(",").toMutableSet()
            } else {
                mutableSetOf()
            }

            failedLevels.add(level.toString())
            failedDownloadsFile.writeText(failedLevels.joinToString(","))
        } catch (e: Exception) {
            // 记录失败不影响主要功能
        }
    }

    // 清理所有本地图片和失败记录（重置功能）
    fun clearAllData() {
        try {
            // 删除所有本地图片
            if (imageDir.exists()) {
                imageDir.listFiles()?.forEach { file ->
                    if (file.name.startsWith("cute_level_")) {
                        file.delete()
                    }
                }
            }

            // 删除失败记录
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.delete()
            }
        } catch (e: Exception) {
            // 清理失败不影响主要功能
        }
    }

    // 重新尝试失败的关卡
    fun retryFailedLevel(level: Int) {
        try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",").toMutableSet()
                failedLevels.remove(level.toString())

                if (failedLevels.isEmpty()) {
                    failedDownloadsFile.delete()
                } else {
                    failedDownloadsFile.writeText(failedLevels.joinToString(","))
                }
            }
        } catch (e: Exception) {
            // 重试失败不影响主要功能
        }
    }

    // 检查本地存储大小
    fun getLocalStorageSize(): Long {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()?.sumOf { it.length() } ?: 0L
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }

    // 获取已下载的关卡列表
    fun getDownloadedLevels(): List<Int> {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()
                    ?.filter { it.name.startsWith("cute_level_") && it.name.endsWith(".png") }
                    ?.mapNotNull { file ->
                        val levelStr = file.name.removePrefix("cute_level_").removeSuffix(".png")
                        levelStr.toIntOrNull()
                    }
                    ?.sorted() ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    // 获取失败的关卡列表
    fun getFailedLevels(): List<Int> {
        return try {
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText()
                    .split(",")
                    .mapNotNull { it.toIntOrNull() }
                    .sorted()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    




    // 从URL下载图片
    private suspend fun downloadImageFromUrl(imageUrl: String): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                Log.d("CuteImageGenerator", "开始下载图片: $imageUrl")
                val url = URL(imageUrl)
                val connection = url.openConnection() as HttpURLConnection

                // 设置请求头，模拟浏览器
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                connection.setRequestProperty("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                connection.setRequestProperty("Referer", "https://image.baidu.com/")

                connection.connectTimeout = 15000
                connection.readTimeout = 30000
                connection.doInput = true
                connection.instanceFollowRedirects = true

                val responseCode = connection.responseCode
                Log.d("CuteImageGenerator", "响应码: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val inputStream: InputStream = connection.inputStream
                    val bitmap = BitmapFactory.decodeStream(inputStream)
                    inputStream.close()
                    connection.disconnect()

                    if (bitmap != null) {
                        Log.d("CuteImageGenerator", "图片下载成功，尺寸: ${bitmap.width}x${bitmap.height}")
                    } else {
                        Log.e("CuteImageGenerator", "图片解码失败")
                    }

                    bitmap
                } else {
                    Log.e("CuteImageGenerator", "HTTP错误: $responseCode")
                    connection.disconnect()
                    null
                }
            } catch (e: Exception) {
                Log.e("CuteImageGenerator", "下载图片异常: $imageUrl", e)
                null
            }
        }
    }
    
    // 调整图片大小
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        return Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
    }

    // 测试下载功能（调试用）
    suspend fun testDownload(level: Int): String {
        return withContext(Dispatchers.IO) {
            try {
                val imageUrls = reliableImageUrls[level] ?: return@withContext "关卡 $level 没有图片URL"
                val result = StringBuilder()

                result.append("测试关卡 $level 的图片下载:\n")
                result.append("可用图片URL数量: ${imageUrls.size}\n\n")

                for ((index, imageUrl) in imageUrls.withIndex()) {
                    result.append("测试URL ${index + 1}: $imageUrl\n")
                    try {
                        val bitmap = downloadImageFromUrl(imageUrl)
                        if (bitmap != null) {
                            result.append("✅ 下载成功！图片尺寸: ${bitmap.width}x${bitmap.height}\n")
                            break // 找到一个可用的就停止
                        } else {
                            result.append("❌ 下载失败\n")
                        }
                    } catch (e: Exception) {
                        result.append("❌ 下载异常: ${e.message}\n")
                    }
                    result.append("\n")
                }

                result.toString()
            } catch (e: Exception) {
                "测试异常: ${e.message}"
            }
        }
    }
}
