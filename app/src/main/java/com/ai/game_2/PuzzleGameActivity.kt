package com.ai.game_2

import android.app.AlertDialog
import android.content.Intent
import android.content.SharedPreferences
import android.graphics.BitmapFactory
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.bumptech.glide.Glide

class PuzzleGameActivity : AppCompatActivity() {
    
    private lateinit var puzzleView: PuzzleView
    private lateinit var levelText: TextView
    private lateinit var difficultyText: TextView
    private lateinit var resetButton: Button
    private lateinit var backButton: Button
    private lateinit var referenceImage: ImageView
    
    private var currentLevel: Int = 1
    private lateinit var currentTheme: GameTheme
    private lateinit var gameLevel: GameLevel
    private lateinit var sharedPreferences: SharedPreferences
    private var customPhotoPath: String? = null
    private var customGridCols: Int = 0
    private var customGridRows: Int = 0
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_puzzle_game)

        sharedPreferences = getSharedPreferences("PuzzleGame", MODE_PRIVATE)
        currentLevel = intent.getIntExtra("level", 1)

        // 获取主题参数
        val themeName = intent.getStringExtra("theme") ?: GameTheme.CUTE.name
        currentTheme = GameTheme.valueOf(themeName)

        // 获取自定义照片路径和网格参数（如果是拍照主题）
        customPhotoPath = intent.getStringExtra("custom_photo_path")
        customGridCols = intent.getIntExtra("custom_grid_cols", 0)
        customGridRows = intent.getIntExtra("custom_grid_rows", 0)

        initializeViews()
        setupGame()
        setupClickListeners()
    }
    
    private fun initializeViews() {
        puzzleView = findViewById(R.id.puzzle_view)
        levelText = findViewById(R.id.tv_level)
        difficultyText = findViewById(R.id.tv_difficulty)
        resetButton = findViewById(R.id.btn_reset)
        backButton = findViewById(R.id.btn_back)
        referenceImage = findViewById(R.id.iv_reference)
    }
    
    private fun setupGame() {
        val levels = GameLevelManager.getLevelsForTheme(currentTheme)
        gameLevel = levels.find { it.levelNumber == currentLevel }
            ?: levels.first()
        
        levelText.text = "第 ${gameLevel.levelNumber} 关"

        // 初始显示，稍后会根据实际网格更新
        difficultyText.text = "${gameLevel.title} (加载中...)"

        // 根据主题类型初始化拼图
        when (currentTheme) {
            GameTheme.CUSTOM_PHOTO -> {
                if (customPhotoPath != null) {
                    // 设置参考图
                    setReferenceImageFromPath(customPhotoPath!!)

                    if (customGridCols > 0 && customGridRows > 0) {
                        // 使用用户自定义的网格
                        puzzleView.initializePuzzleWithCustomPhotoAndGrid(customPhotoPath!!, customGridCols, customGridRows)
                        updateDifficultyText(customGridCols, customGridRows)
                    } else {
                        // 使用智能网格
                        puzzleView.initializePuzzleWithCustomPhoto(customPhotoPath!!)
                        // 智能网格需要等待计算完成后更新
                        puzzleView.post {
                            updateDifficultyText(puzzleView.gridCols, puzzleView.gridRows)
                        }
                    }
                }
            }
            GameTheme.CUTE -> {
                // 显示加载提示
                showLoadingIndicator()

                // 可爱主题使用程序生成的图片
                puzzleView.initializePuzzleWithGeneratedImage(currentLevel) { bitmap ->
                    // 隐藏加载提示
                    hideLoadingIndicator()

                    // 设置参考图
                    if (bitmap != null) {
                        referenceImage.setImageBitmap(bitmap)
                    }

                    // 更新难度显示（可爱主题固定3x3）
                    updateDifficultyText(3, 3)
                }
            }
            GameTheme.VOICE_SEARCH -> {
                // 语音搜图主题使用URL加载
                val imageUrls = intent.getStringArrayListExtra("image_urls")
                android.util.Log.d("PuzzleGameActivity", "语音搜图主题，关卡: $currentLevel, 图片URLs: $imageUrls")

                if (imageUrls != null && currentLevel <= imageUrls.size) {
                    val imageUrl = imageUrls[currentLevel - 1]
                    android.util.Log.d("PuzzleGameActivity", "当前关卡图片URL: $imageUrl")

                    // 设置参考图 - 修复显示问题
                    if (!imageUrl.startsWith("voice_search_placeholder_") && !imageUrl.startsWith("default_image_")) {
                        // 真实图片URL，使用Glide加载参考图
                        android.util.Log.d("PuzzleGameActivity", "加载真实图片作为参考图: $imageUrl")

                        // 先显示占位符
                        referenceImage.setImageResource(R.drawable.ic_voice_placeholder)

                        Glide.with(this)
                            .load(imageUrl)
                            .placeholder(R.drawable.ic_voice_placeholder)
                            .error(R.drawable.ic_voice_placeholder)
                            .timeout(15000) // 15秒超时
                            .listener(object : com.bumptech.glide.request.RequestListener<android.graphics.drawable.Drawable> {
                                override fun onLoadFailed(
                                    e: com.bumptech.glide.load.engine.GlideException?,
                                    model: Any?,
                                    target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>?,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    android.util.Log.e("PuzzleGameActivity", "参考图加载失败: $imageUrl", e)
                                    // 加载失败时保持占位符
                                    referenceImage.setImageResource(R.drawable.ic_voice_placeholder)
                                    return true // 阻止Glide设置error drawable
                                }

                                override fun onResourceReady(
                                    resource: android.graphics.drawable.Drawable?,
                                    model: Any?,
                                    target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>?,
                                    dataSource: com.bumptech.glide.load.DataSource?,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    android.util.Log.d("PuzzleGameActivity", "参考图加载成功: $imageUrl")
                                    return false // 让Glide正常设置图片
                                }
                            })
                            .into(referenceImage)
                    } else {
                        // 默认图片
                        android.util.Log.d("PuzzleGameActivity", "使用默认图片作为参考图")
                        referenceImage.setImageResource(R.drawable.ic_voice_placeholder)
                    }

                    puzzleView.initializePuzzleWithUrl(imageUrl) {
                        // 初始化完成后更新难度显示
                        puzzleView.post {
                            updateDifficultyText(puzzleView.gridCols, puzzleView.gridRows)
                        }
                    }
                } else {
                    android.util.Log.e("PuzzleGameActivity", "语音搜图主题图片URLs为空或关卡超出范围")
                }
            }
            else -> {
                // 其他主题使用资源图片
                if (gameLevel.imageResourceId != 0) {
                    referenceImage.setImageResource(gameLevel.imageResourceId)
                }
                puzzleView.initializePuzzle(gameLevel.imageResourceId, gameLevel.gridSize)
                // 其他主题固定3x3
                updateDifficultyText(3, 3)
            }
        }
        
        puzzleView.onPuzzleCompleted = {
            Log.d("PuzzleGameActivity", "收到拼图完成回调")
            onPuzzleCompleted()
        }
    }

    private fun setReferenceImageFromPath(imagePath: String) {
        try {
            val bitmap = BitmapFactory.decodeFile(imagePath)
            if (bitmap != null) {
                referenceImage.setImageBitmap(bitmap)
            }
        } catch (e: Exception) {
            // 设置失败时使用默认图片
            referenceImage.setImageResource(R.drawable.ic_camera_placeholder)
        }
    }

    private fun updateDifficultyText(cols: Int, rows: Int) {
        val totalPieces = cols * rows
        val gridText = "${cols}×${rows} (${totalPieces}块)"
        difficultyText.text = "${gameLevel.title} ($gridText)"
    }
    
    private fun setupClickListeners() {
        resetButton.setOnClickListener {
            showResetConfirmDialog()
        }
        
        backButton.setOnClickListener {
            finish()
        }
    }
    
    private fun showResetConfirmDialog() {
        AlertDialog.Builder(this)
            .setTitle("重置拼图")
            .setMessage("确定要重新开始这一关吗？")
            .setPositiveButton("确定") { _, _ ->
                puzzleView.resetPuzzle()
                Toast.makeText(this, "拼图已重置", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun onPuzzleCompleted() {
        Log.d("PuzzleGameActivity", "拼图完成处理开始")
        // 保存关卡进度
        saveProgress()

        // 显示完成对话框
        showCompletionDialog()
    }
    
    private fun saveProgress() {
        val editor = sharedPreferences.edit()
        val maxUnlockedLevelKey = "max_unlocked_level_${currentTheme.name}"
        val maxUnlockedLevel = sharedPreferences.getInt(maxUnlockedLevelKey, 1)

        if (currentLevel >= maxUnlockedLevel) {
            editor.putInt(maxUnlockedLevelKey, currentLevel + 1)
        }

        editor.putBoolean("level_${currentTheme.name}_${currentLevel}_completed", true)
        editor.apply()
    }
    
    private fun showCompletionDialog() {
        showCongratulationsAnimation()
    }

    private fun showCongratulationsAnimation() {
        // 创建恭喜动画覆盖层
        val inflater = LayoutInflater.from(this)
        val congratulationsView = inflater.inflate(R.layout.congratulations_overlay, null)

        // 添加到根布局
        val rootLayout = findViewById<FrameLayout>(android.R.id.content)
        rootLayout.addView(congratulationsView)

        // 获取视图元素
        val container = congratulationsView.findViewById<LinearLayout>(R.id.congratulationsContainer)
        val levelText = congratulationsView.findViewById<TextView>(R.id.levelCompletedText)
        val characterText = congratulationsView.findViewById<TextView>(R.id.characterNameText)
        val encouragementText = congratulationsView.findViewById<TextView>(R.id.encouragementText)
        val backButton = congratulationsView.findViewById<Button>(R.id.backToMenuButton)
        val nextButton = congratulationsView.findViewById<Button>(R.id.nextLevelButton)
        val closeButton = congratulationsView.findViewById<Button>(R.id.closeButton)

        // 设置文本内容
        levelText.text = "第 $currentLevel 关完成！"
        characterText.text = gameLevel.title

        // 随机鼓励文字
        val encouragements = arrayOf(
            "太棒了！你真厉害！ 💖",
            "哇！你好聪明！ ✨",
            "完美！继续加油！ 🌟",
            "真是太棒了！ 🎉",
            "你是拼图小天才！ 👑"
        )
        encouragementText.text = encouragements.random()

        // 检查是否是最后一关
        val totalLevels = when (currentTheme) {
            GameTheme.CUSTOM_PHOTO -> {
                // 拍照主题：基于照片数量
                intent.getStringArrayListExtra("photo_paths")?.size ?: 1
            }
            GameTheme.VOICE_SEARCH -> {
                // 语音搜图主题：基于搜索结果数量
                intent.getStringArrayListExtra("image_urls")?.size ?: 1
            }
            else -> {
                // 其他主题：使用默认关卡数量
                GameLevelManager.getLevelsForTheme(currentTheme).size
            }
        }

        val isLastLevel = currentLevel >= totalLevels
        if (isLastLevel) {
            nextButton.text = "🏆 完成游戏"
            nextButton.setOnClickListener {
                showGameCompletedDialog()
                rootLayout.removeView(congratulationsView)
            }
        } else {
            nextButton.setOnClickListener {
                rootLayout.removeView(congratulationsView)
                startNextLevel()
            }
        }

        backButton.setOnClickListener {
            rootLayout.removeView(congratulationsView)
            finish()
        }

        closeButton.setOnClickListener {
            rootLayout.removeView(congratulationsView)
        }

        // 开始动画
        startCongratulationsAnimations(congratulationsView, container)
    }

    private fun startCongratulationsAnimations(overlayView: View, container: LinearLayout) {
        // 容器缩放动画
        val scaleAnimation = AnimationUtils.loadAnimation(this, R.anim.congratulations_scale_in)
        container.startAnimation(scaleAnimation)

        // 星星闪烁动画
        val starsContainer = overlayView.findViewById<LinearLayout>(R.id.starsContainer)
        val star1 = starsContainer.findViewById<TextView>(R.id.star1)
        val star2 = starsContainer.findViewById<TextView>(R.id.star2)
        val star3 = starsContainer.findViewById<TextView>(R.id.star3)

        val twinkleAnimation = AnimationUtils.loadAnimation(this, R.anim.star_twinkle)

        // 延迟启动星星动画
        Handler(Looper.getMainLooper()).postDelayed({
            star1.startAnimation(twinkleAnimation)
        }, 500)

        Handler(Looper.getMainLooper()).postDelayed({
            star2.startAnimation(twinkleAnimation)
        }, 700)

        Handler(Looper.getMainLooper()).postDelayed({
            star3.startAnimation(twinkleAnimation)
        }, 900)

        // 飘落星星动画
        startFallingStarsAnimation(overlayView)
    }

    private fun startFallingStarsAnimation(overlayView: View) {
        val fallingStars = listOf(
            overlayView.findViewById<ImageView>(R.id.fallingStars1),
            overlayView.findViewById<ImageView>(R.id.fallingStars2),
            overlayView.findViewById<ImageView>(R.id.fallingStars3),
            overlayView.findViewById<ImageView>(R.id.fallingStars4)
        )

        val fallAnimation = AnimationUtils.loadAnimation(this, R.anim.star_fall)

        fallingStars.forEachIndexed { index, star ->
            Handler(Looper.getMainLooper()).postDelayed({
                star.visibility = View.VISIBLE
                star.startAnimation(fallAnimation)
            }, (index * 300).toLong())
        }
    }

    private fun showGameCompletedDialog() {
        AlertDialog.Builder(this)
            .setTitle("🎉 游戏完成！")
            .setMessage("恭喜你完成了所有关卡！\n你真是太厉害了！ 👑")
            .setPositiveButton("返回主菜单") { _, _ ->
                finish()
            }
            .setCancelable(false)
            .show()
    }
    
    private fun startNextLevel() {
        val nextLevelIntent = Intent(this, PuzzleGameActivity::class.java)
        nextLevelIntent.putExtra("theme", currentTheme.name)
        nextLevelIntent.putExtra("level", currentLevel + 1)

        // 传递拍照主题的额外参数
        if (currentTheme == GameTheme.CUSTOM_PHOTO) {
            val photoPath = intent.getStringExtra("custom_photo_path")
            val gridCols = intent.getIntExtra("custom_grid_cols", 3)
            val photoPaths = intent.getStringArrayListExtra("photo_paths")

            if (photoPaths != null && currentLevel < photoPaths.size) {
                val nextPhotoPath = photoPaths[currentLevel] // currentLevel是下一关的索引(从0开始)
                nextLevelIntent.putExtra("custom_photo_path", nextPhotoPath)
                nextLevelIntent.putExtra("custom_grid_cols", gridCols)
                nextLevelIntent.putExtra("useDynamicGrid", true)
                nextLevelIntent.putStringArrayListExtra("photo_paths", photoPaths)
            }
        }

        // 传递语音搜图主题的额外参数
        if (currentTheme == GameTheme.VOICE_SEARCH) {
            val searchQuery = intent.getStringExtra("search_query")
            val imageUrls = intent.getStringArrayListExtra("image_urls")
            val useDynamicGrid = intent.getBooleanExtra("useDynamicGrid", false)

            nextLevelIntent.putExtra("search_query", searchQuery)
            nextLevelIntent.putStringArrayListExtra("image_urls", imageUrls)
            nextLevelIntent.putExtra("useDynamicGrid", useDynamicGrid)
        }

        startActivity(nextLevelIntent)
        finish()
    }

    private fun showLoadingIndicator() {
        // 在拼图视图上显示加载提示
        val loadingText = TextView(this).apply {
            text = "正在加载图片..."
            textSize = 18f
            setTextColor(getColor(R.color.primary_text_color))
            gravity = android.view.Gravity.CENTER
            setBackgroundColor(getColor(R.color.background_color))
            alpha = 0.9f
        }

        val rootLayout = findViewById<FrameLayout>(android.R.id.content)
        val layoutParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )
        loadingText.layoutParams = layoutParams
        loadingText.tag = "loading_indicator"
        rootLayout.addView(loadingText)
    }

    private fun hideLoadingIndicator() {
        val rootLayout = findViewById<FrameLayout>(android.R.id.content)
        val loadingView = rootLayout.findViewWithTag<TextView>("loading_indicator")
        loadingView?.let {
            rootLayout.removeView(it)
        }
    }
}
