package com.ai.game_2

import android.content.Context
import android.graphics.*
import android.util.Log
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.cos
import kotlin.math.sin

class CuteImageGenerator(private val context: Context) {

    private val width = 400
    private val height = 400

    // 图片永久存储目录（使用内部存储，不会被清理）
    private val imageDir = File(context.filesDir, "cute_images")

    // 下载失败记录文件
    private val failedDownloadsFile = File(context.filesDir, "failed_downloads.txt")

    // 简笔画搜索关键词
    private val cuteThemes = listOf(
        "cat simple drawing outline",
        "car simple drawing outline",
        "dinosaur simple drawing outline",
        "castle simple drawing outline",
        "rocket simple drawing outline",
        "flower simple drawing outline",
        "robot simple drawing outline",
        "butterfly simple drawing outline",
        "house simple drawing outline",
        "unicorn simple drawing outline"
    )

    init {
        // 创建图片存储目录
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        }
    }

    // 可靠的高清图片源（无版权问题）
    private val reliableImageSources = listOf(
        "https://picsum.photos/800/800?random=",
        "https://picsum.photos/1000/1000?random=",
        "https://picsum.photos/600/600?random=",
        "https://picsum.photos/900/900?random="
    )

    // 可爱的儿童友好高清图片URL（支持100个关卡）
    private fun getImageUrlsForLevel(level: Int): List<String> {
        // 专门的儿童友好、可爱卡通主题
        val cuteAnimals = listOf(
            "cute+kitten", "adorable+puppy", "kawaii+bunny", "cute+bear", "adorable+panda",
            "kawaii+cat", "cute+dog", "adorable+hamster", "kawaii+fox", "cute+penguin",
            "adorable+koala", "kawaii+elephant", "cute+giraffe", "adorable+lion", "kawaii+tiger",
            "cute+monkey", "adorable+pig", "kawaii+cow", "cute+sheep", "adorable+duck"
        )

        val cuteCharacters = listOf(
            "kawaii+princess", "cute+fairy", "adorable+angel", "kawaii+unicorn", "cute+mermaid",
            "adorable+witch", "kawaii+robot", "cute+alien", "adorable+ghost", "kawaii+monster",
            "cute+superhero", "adorable+pirate", "kawaii+ninja", "cute+wizard", "adorable+knight",
            "kawaii+clown", "cute+chef", "adorable+doctor", "kawaii+teacher", "cute+artist"
        )

        val cuteFood = listOf(
            "kawaii+cake", "cute+cupcake", "adorable+donut", "kawaii+icecream", "cute+candy",
            "adorable+cookie", "kawaii+lollipop", "cute+chocolate", "adorable+fruit", "kawaii+pizza",
            "cute+burger", "adorable+hotdog", "kawaii+sushi", "cute+bread", "adorable+pie",
            "kawaii+juice", "cute+milk", "adorable+tea", "kawaii+coffee", "cute+smoothie"
        )

        val cuteObjects = listOf(
            "kawaii+toy", "cute+doll", "adorable+teddy", "kawaii+ball", "cute+car",
            "adorable+train", "kawaii+plane", "cute+boat", "adorable+rocket", "kawaii+castle",
            "cute+house", "adorable+tree", "kawaii+flower", "cute+star", "adorable+moon",
            "kawaii+sun", "cute+cloud", "adorable+rainbow", "kawaii+heart", "cute+diamond"
        )

        val cuteSeasons = listOf(
            "kawaii+christmas", "cute+halloween", "adorable+easter", "kawaii+birthday", "cute+valentine",
            "adorable+spring", "kawaii+summer", "cute+autumn", "adorable+winter", "kawaii+holiday",
            "cute+party", "adorable+celebration", "kawaii+festival", "cute+carnival", "adorable+picnic",
            "kawaii+beach", "cute+garden", "adorable+park", "kawaii+playground", "cute+school"
        )

        // 合并所有主题
        val allThemes = cuteAnimals + cuteCharacters + cuteFood + cuteObjects + cuteSeasons

        val themeIndex = (level - 1) % allThemes.size
        val theme = allThemes[themeIndex]

        return listOf(
            // 使用更可靠的图片源 - Picsum高清图片
            "https://picsum.photos/800/800?random=${level}",
            "https://picsum.photos/800/800?random=${level + 100}",
            "https://picsum.photos/800/800?random=${level + 200}",
            "https://picsum.photos/800/800?random=${level + 300}",
            "https://picsum.photos/800/800?random=${level + 400}"
        )
    }
    
    // 检查关卡是否可用（下载是否成功）
    fun isLevelAvailable(level: Int): Boolean {
        // 检查是否有本地图片
        if (hasLocalImage(level)) {
            return true
        }

        // 检查是否在失败列表中
        return !isLevelFailed(level)
    }

    // 异步生成可爱图片
    suspend fun generateCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 检查关卡是否可用
                if (!isLevelAvailable(level)) {
                    return@withContext null // 关卡不可用，返回null
                }

                // 尝试从本地或网络获取图片
                downloadCuteImage(level)
            } catch (e: Exception) {
                // 下载失败，标记关卡为不可用
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 同步版本（保持兼容性）
    fun generateCuteImageSync(level: Int): Bitmap? {
        return runBlocking {
            generateCuteImage(level)
        }
    }
    
    // 从本地或网络获取儿童友好的高清可爱图片
    private suspend fun downloadCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 首先检查本地是否已有图片
                val localBitmap = getLocalImage(level)
                if (localBitmap != null) {
                    return@withContext localBitmap // 直接返回本地图片，不重复下载
                }

                // 本地没有，从可靠的URL下载图片
                Log.d("CuteImageGenerator", "开始为关卡 $level 下载图片")
                val imageUrls = getImageUrlsForLevel(level)
                for (imageUrl in imageUrls) {
                    try {
                        Log.d("CuteImageGenerator", "尝试下载: $imageUrl")
                        val bitmap = downloadAndSaveImage(imageUrl, level)
                        if (bitmap != null) {
                            Log.d("CuteImageGenerator", "关卡 $level 高清图片下载成功，尺寸: ${bitmap.width}x${bitmap.height}")
                            return@withContext bitmap
                        } else {
                            Log.d("CuteImageGenerator", "下载失败: $imageUrl")
                        }
                    } catch (e: Exception) {
                        Log.e("CuteImageGenerator", "下载异常: $imageUrl", e)
                        continue
                    }
                }



                // 主要源失败，尝试可靠的备用源
                Log.d("CuteImageGenerator", "主要源失败，尝试可靠备用源")
                for ((index, baseUrl) in reliableImageSources.withIndex()) {
                    try {
                        val backupUrl = "$baseUrl${level + index * 1000}"
                        val bitmap = downloadAndSaveImage(backupUrl, level)
                        if (bitmap != null) {
                            Log.d("CuteImageGenerator", "关卡 $level 备用源下载成功")
                            return@withContext bitmap
                        }
                    } catch (e: Exception) {
                        Log.e("CuteImageGenerator", "备用源下载异常", e)
                        continue
                    }
                }

                // 所有网络源都失败，生成本地可爱图片
                Log.d("CuteImageGenerator", "网络源都失败，生成本地可爱图片")
                generateLocalCuteImage(level)
            } catch (e: Exception) {
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 检查本地是否有图片
    private fun hasLocalImage(level: Int): Boolean {
        val imageFile = File(imageDir, "cute_level_$level.png")
        return imageFile.exists()
    }

    // 获取本地图片
    private fun getLocalImage(level: Int): Bitmap? {
        return try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            if (imageFile.exists()) {
                BitmapFactory.decodeFile(imageFile.absolutePath)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    // 下载图片并永久保存
    private suspend fun downloadAndSaveImage(imageUrl: String, level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val bitmap = downloadImageFromUrl(imageUrl)
                if (bitmap != null) {
                    // 永久保存到本地
                    saveBitmapToLocal(bitmap, level)
                    bitmap
                } else {
                    null
                }
            } catch (e: Exception) {
                null
            }
        }
    }

    // 永久保存图片到本地
    private fun saveBitmapToLocal(bitmap: Bitmap, level: Int) {
        try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            val outputStream = FileOutputStream(imageFile)
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream) // 最高质量保存
            outputStream.close()
        } catch (e: Exception) {
            // 保存失败不影响主要功能
        }
    }

    // 检查关卡是否下载失败
    private fun isLevelFailed(level: Int): Boolean {
        return try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",")
                failedLevels.contains(level.toString())
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }

    // 标记关卡为下载失败
    private fun markLevelAsFailed(level: Int) {
        try {
            val failedLevels = if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText().split(",").toMutableSet()
            } else {
                mutableSetOf()
            }

            failedLevels.add(level.toString())
            failedDownloadsFile.writeText(failedLevels.joinToString(","))
        } catch (e: Exception) {
            // 记录失败不影响主要功能
        }
    }

    // 清理所有本地图片和失败记录（重置功能）
    fun clearAllData() {
        try {
            // 删除所有本地图片
            if (imageDir.exists()) {
                imageDir.listFiles()?.forEach { file ->
                    if (file.name.startsWith("cute_level_")) {
                        file.delete()
                    }
                }
            }

            // 删除失败记录
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.delete()
            }
        } catch (e: Exception) {
            // 清理失败不影响主要功能
        }
    }

    // 重新尝试失败的关卡
    fun retryFailedLevel(level: Int) {
        try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",").toMutableSet()
                failedLevels.remove(level.toString())

                if (failedLevels.isEmpty()) {
                    failedDownloadsFile.delete()
                } else {
                    failedDownloadsFile.writeText(failedLevels.joinToString(","))
                }
            }
        } catch (e: Exception) {
            // 重试失败不影响主要功能
        }
    }

    // 检查本地存储大小
    fun getLocalStorageSize(): Long {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()?.sumOf { it.length() } ?: 0L
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }

    // 获取已下载的关卡列表
    fun getDownloadedLevels(): List<Int> {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()
                    ?.filter { it.name.startsWith("cute_level_") && it.name.endsWith(".png") }
                    ?.mapNotNull { file ->
                        val levelStr = file.name.removePrefix("cute_level_").removeSuffix(".png")
                        levelStr.toIntOrNull()
                    }
                    ?.sorted() ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    // 获取失败的关卡列表
    fun getFailedLevels(): List<Int> {
        return try {
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText()
                    .split(",")
                    .mapNotNull { it.toIntOrNull() }
                    .sorted()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    




    // 从URL下载图片
    private suspend fun downloadImageFromUrl(imageUrl: String): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                Log.d("CuteImageGenerator", "开始下载图片: $imageUrl")
                val url = URL(imageUrl)
                val connection = url.openConnection() as HttpURLConnection

                // 设置请求头，模拟浏览器
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                connection.setRequestProperty("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                connection.setRequestProperty("Referer", "https://image.baidu.com/")

                connection.connectTimeout = 15000
                connection.readTimeout = 30000
                connection.doInput = true
                connection.instanceFollowRedirects = true

                val responseCode = connection.responseCode
                Log.d("CuteImageGenerator", "响应码: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val inputStream: InputStream = connection.inputStream
                    val bitmap = BitmapFactory.decodeStream(inputStream)
                    inputStream.close()
                    connection.disconnect()

                    if (bitmap != null) {
                        Log.d("CuteImageGenerator", "图片下载成功，尺寸: ${bitmap.width}x${bitmap.height}")
                    } else {
                        Log.e("CuteImageGenerator", "图片解码失败")
                    }

                    bitmap
                } else {
                    Log.e("CuteImageGenerator", "HTTP错误: $responseCode")
                    connection.disconnect()
                    null
                }
            } catch (e: Exception) {
                Log.e("CuteImageGenerator", "下载图片异常: $imageUrl", e)
                null
            }
        }
    }
    
    // 调整图片大小 - 保持高清质量
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        // 如果原图已经是目标尺寸或更大，保持原图质量
        if (bitmap.width >= targetWidth && bitmap.height >= targetHeight) {
            // 计算缩放比例，保持宽高比
            val scaleX = targetWidth.toFloat() / bitmap.width
            val scaleY = targetHeight.toFloat() / bitmap.height
            val scale = maxOf(scaleX, scaleY) // 使用较大的缩放比例确保填满

            val scaledWidth = (bitmap.width * scale).toInt()
            val scaledHeight = (bitmap.height * scale).toInt()

            // 使用高质量缩放
            val scaledBitmap = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true)

            // 如果缩放后的图片比目标尺寸大，进行居中裁剪
            return if (scaledWidth > targetWidth || scaledHeight > targetHeight) {
                val x = maxOf(0, (scaledWidth - targetWidth) / 2)
                val y = maxOf(0, (scaledHeight - targetHeight) / 2)
                Bitmap.createBitmap(scaledBitmap, x, y,
                    minOf(targetWidth, scaledWidth),
                    minOf(targetHeight, scaledHeight))
            } else {
                scaledBitmap
            }
        } else {
            // 原图太小，直接缩放
            return Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
        }
    }

    // 生成本地可爱图片（当网络图片都失败时使用）
    private fun generateLocalCuteImage(level: Int): Bitmap? {
        return try {
            val width = 800
            val height = 800
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            val paint = Paint(Paint.ANTI_ALIAS_FLAG)

            // 根据关卡生成不同的可爱背景色
            val colors = listOf(
                Color.parseColor("#FFE4E1"), // 粉色
                Color.parseColor("#E6F3FF"), // 浅蓝
                Color.parseColor("#F0FFF0"), // 浅绿
                Color.parseColor("#FFF8DC"), // 浅黄
                Color.parseColor("#F5F0FF"), // 浅紫
                Color.parseColor("#FFE4CC"), // 浅橙
                Color.parseColor("#E0FFFF"), // 浅青
                Color.parseColor("#FFF0F5")  // 浅玫瑰
            )

            val bgColor = colors[level % colors.size]
            canvas.drawColor(bgColor)

            // 绘制可爱的图案
            paint.color = Color.WHITE
            paint.style = Paint.Style.FILL

            // 绘制可爱的圆形和心形
            val centerX = width / 2f
            val centerY = height / 2f

            // 主圆形
            canvas.drawCircle(centerX, centerY, 200f, paint)

            // 可爱的眼睛
            paint.color = Color.BLACK
            canvas.drawCircle(centerX - 60f, centerY - 40f, 20f, paint)
            canvas.drawCircle(centerX + 60f, centerY - 40f, 20f, paint)

            // 可爱的嘴巴
            paint.style = Paint.Style.STROKE
            paint.strokeWidth = 8f
            canvas.drawArc(centerX - 40f, centerY + 20f, centerX + 40f, centerY + 80f, 0f, 180f, false, paint)

            // 可爱的腮红
            paint.style = Paint.Style.FILL
            paint.color = Color.parseColor("#FFB6C1")
            canvas.drawCircle(centerX - 120f, centerY + 20f, 25f, paint)
            canvas.drawCircle(centerX + 120f, centerY + 20f, 25f, paint)

            // 装饰性的小星星
            paint.color = Color.parseColor("#FFD700")
            drawStar(canvas, paint, centerX - 250f, centerY - 200f, 30f)
            drawStar(canvas, paint, centerX + 250f, centerY - 200f, 30f)
            drawStar(canvas, paint, centerX - 250f, centerY + 200f, 30f)
            drawStar(canvas, paint, centerX + 250f, centerY + 200f, 30f)

            // 保存到本地
            saveBitmapToLocal(bitmap, level)

            Log.d("CuteImageGenerator", "关卡 $level 本地可爱图片生成成功")
            bitmap
        } catch (e: Exception) {
            Log.e("CuteImageGenerator", "生成本地可爱图片失败", e)
            markLevelAsFailed(level)
            null
        }
    }

    // 绘制星星的辅助方法
    private fun drawStar(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float, radius: Float) {
        val path = Path()
        val angle = Math.PI / 5.0

        for (i in 0..9) {
            val r = if (i % 2 == 0) radius else radius * 0.5f
            val x = centerX + (r * cos(i * angle - Math.PI / 2)).toFloat()
            val y = centerY + (r * sin(i * angle - Math.PI / 2)).toFloat()

            if (i == 0) {
                path.moveTo(x, y)
            } else {
                path.lineTo(x, y)
            }
        }
        path.close()
        canvas.drawPath(path, paint)
    }


}
