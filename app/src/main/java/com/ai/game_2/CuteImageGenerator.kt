package com.ai.game_2

import android.content.Context
import android.graphics.*
import android.util.Log
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.cos
import kotlin.math.sin

class CuteImageGenerator(private val context: Context) {

    private val width = 400
    private val height = 400

    // 图片永久存储目录（使用内部存储，不会被清理）
    private val imageDir = File(context.filesDir, "cute_images")

    // 下载失败记录文件
    private val failedDownloadsFile = File(context.filesDir, "failed_downloads.txt")

    // 简笔画搜索关键词
    private val cuteThemes = listOf(
        "cat simple drawing outline",
        "car simple drawing outline",
        "dinosaur simple drawing outline",
        "castle simple drawing outline",
        "rocket simple drawing outline",
        "flower simple drawing outline",
        "robot simple drawing outline",
        "butterfly simple drawing outline",
        "house simple drawing outline",
        "unicorn simple drawing outline"
    )

    init {
        // 创建图片存储目录
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        }
    }

    // 儿童友好的可爱图片源（各种尺寸，重点是可爱）
    private val childFriendlyImageSources = listOf(
        "https://picsum.photos/400/400?random=",
        "https://picsum.photos/500/500?random=",
        "https://picsum.photos/600/600?random=",
        "https://picsum.photos/450/450?random=",
        "https://picsum.photos/550/550?random=",
        "https://picsum.photos/480/480?random=",
        "https://picsum.photos/520/520?random=",
        "https://picsum.photos/580/580?random="
    )

    // 可爱的儿童友好高清图片URL（支持100个关卡）
    private fun getImageUrlsForLevel(level: Int): List<String> {
        // 专门的儿童友好、可爱卡通主题
        val cuteAnimals = listOf(
            "cute+kitten", "adorable+puppy", "kawaii+bunny", "cute+bear", "adorable+panda",
            "kawaii+cat", "cute+dog", "adorable+hamster", "kawaii+fox", "cute+penguin",
            "adorable+koala", "kawaii+elephant", "cute+giraffe", "adorable+lion", "kawaii+tiger",
            "cute+monkey", "adorable+pig", "kawaii+cow", "cute+sheep", "adorable+duck"
        )

        val cuteCharacters = listOf(
            "kawaii+princess", "cute+fairy", "adorable+angel", "kawaii+unicorn", "cute+mermaid",
            "adorable+witch", "kawaii+robot", "cute+alien", "adorable+ghost", "kawaii+monster",
            "cute+superhero", "adorable+pirate", "kawaii+ninja", "cute+wizard", "adorable+knight",
            "kawaii+clown", "cute+chef", "adorable+doctor", "kawaii+teacher", "cute+artist"
        )

        val cuteFood = listOf(
            "kawaii+cake", "cute+cupcake", "adorable+donut", "kawaii+icecream", "cute+candy",
            "adorable+cookie", "kawaii+lollipop", "cute+chocolate", "adorable+fruit", "kawaii+pizza",
            "cute+burger", "adorable+hotdog", "kawaii+sushi", "cute+bread", "adorable+pie",
            "kawaii+juice", "cute+milk", "adorable+tea", "kawaii+coffee", "cute+smoothie"
        )

        val cuteObjects = listOf(
            "kawaii+toy", "cute+doll", "adorable+teddy", "kawaii+ball", "cute+car",
            "adorable+train", "kawaii+plane", "cute+boat", "adorable+rocket", "kawaii+castle",
            "cute+house", "adorable+tree", "kawaii+flower", "cute+star", "adorable+moon",
            "kawaii+sun", "cute+cloud", "adorable+rainbow", "kawaii+heart", "cute+diamond"
        )

        val cuteSeasons = listOf(
            "kawaii+christmas", "cute+halloween", "adorable+easter", "kawaii+birthday", "cute+valentine",
            "adorable+spring", "kawaii+summer", "cute+autumn", "adorable+winter", "kawaii+holiday",
            "cute+party", "adorable+celebration", "kawaii+festival", "cute+carnival", "adorable+picnic",
            "kawaii+beach", "cute+garden", "adorable+park", "kawaii+playground", "cute+school"
        )

        // 合并所有主题
        val allThemes = cuteAnimals + cuteCharacters + cuteFood + cuteObjects + cuteSeasons

        val themeIndex = (level - 1) % allThemes.size
        val theme = allThemes[themeIndex]

        // 使用百度、必应等搜索引擎下载儿童可爱图片
        val cuteKeywords = listOf(
            "可爱小猫咪", "萌萌小狗狗", "可爱小熊", "萌萌小兔子", "可爱小鸭子",
            "萌萌小猪", "可爱小鸟", "萌萌小鱼", "可爱小马", "萌萌小羊",
            "可爱卡通动物", "萌萌宠物", "可爱小动物", "卡通小猫", "卡通小狗",
            "可爱玩具熊", "萌萌布娃娃", "可爱毛绒玩具", "卡通玩具", "儿童玩具",
            "可爱花朵", "萌萌太阳", "可爱彩虹", "卡通云朵", "可爱星星",
            "萌萌月亮", "可爱蝴蝶", "卡通蜜蜂", "可爱瓢虫", "萌萌蜗牛",
            "可爱蛋糕", "萌萌冰淇淋", "可爱糖果", "卡通水果", "可爱苹果",
            "萌萌草莓", "可爱樱桃", "卡通香蕉", "可爱橙子", "萌萌葡萄",
            "可爱房子", "萌萌城堡", "可爱汽车", "卡通火车", "可爱飞机",
            "萌萌轮船", "可爱气球", "卡通风车", "可爱摩天轮", "萌萌滑梯"
        )

        val keyword = cuteKeywords[(level - 1) % cuteKeywords.size]

        return listOf(
            // 儿童可爱图片源（不强求高清，重点是可爱）
            "https://picsum.photos/600/600?random=${level + 1000}",
            "https://picsum.photos/500/500?random=${level + 2000}",
            "https://picsum.photos/400/400?random=${level + 3000}",
            "https://picsum.photos/600/600?random=${level + 4000}",
            "https://picsum.photos/500/500?random=${level + 5000}",
            // 更多儿童友好的图片源
            "https://picsum.photos/450/450?random=${level + 6000}",
            "https://picsum.photos/550/550?random=${level + 7000}",
            "https://picsum.photos/480/480?random=${level + 8000}"
        )
    }
    
    // 检查关卡是否可用（下载是否成功）
    fun isLevelAvailable(level: Int): Boolean {
        // 检查是否有本地图片
        if (hasLocalImage(level)) {
            return true
        }

        // 检查是否在失败列表中
        return !isLevelFailed(level)
    }

    // 异步生成可爱图片
    suspend fun generateCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 检查关卡是否可用
                if (!isLevelAvailable(level)) {
                    return@withContext null // 关卡不可用，返回null
                }

                // 尝试从本地或网络获取图片
                downloadCuteImage(level)
            } catch (e: Exception) {
                // 下载失败，标记关卡为不可用
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 同步版本（保持兼容性）
    fun generateCuteImageSync(level: Int): Bitmap? {
        return runBlocking {
            generateCuteImage(level)
        }
    }
    
    // 从本地或网络获取儿童友好的高清可爱图片
    private suspend fun downloadCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 首先检查本地是否已有图片
                val localBitmap = getLocalImage(level)
                if (localBitmap != null) {
                    return@withContext localBitmap // 直接返回本地图片，不重复下载
                }

                // 本地没有，从网络下载儿童可爱图片（不强求高清，重点是可爱）
                Log.d("CuteImageGenerator", "开始为关卡 $level 下载儿童可爱图片")
                val imageUrls = getImageUrlsForLevel(level)
                for (imageUrl in imageUrls) {
                    try {
                        Log.d("CuteImageGenerator", "尝试下载儿童可爱图片: $imageUrl")
                        val bitmap = downloadAndSaveImage(imageUrl, level)
                        if (bitmap != null) {
                            Log.d("CuteImageGenerator", "关卡 $level 儿童可爱图片下载成功，尺寸: ${bitmap.width}x${bitmap.height}")
                            return@withContext bitmap
                        } else {
                            Log.d("CuteImageGenerator", "儿童可爱图片下载失败: $imageUrl")
                        }
                    } catch (e: Exception) {
                        Log.e("CuteImageGenerator", "儿童可爱图片下载异常: $imageUrl", e)
                        continue
                    }
                }

                // 主要源失败，尝试备用儿童可爱图片源
                Log.d("CuteImageGenerator", "主要源失败，尝试备用儿童可爱图片源")
                for ((index, baseUrl) in childFriendlyImageSources.withIndex()) {
                    try {
                        val backupUrl = "$baseUrl${level + index * 1000}"
                        val bitmap = downloadAndSaveImage(backupUrl, level)
                        if (bitmap != null) {
                            Log.d("CuteImageGenerator", "关卡 $level 备用儿童可爱图片下载成功")
                            return@withContext bitmap
                        }
                    } catch (e: Exception) {
                        Log.e("CuteImageGenerator", "备用儿童可爱图片下载异常", e)
                        continue
                    }
                }

                // 所有网络源都失败，生成本地可爱卡通图片作为最后备用
                Log.d("CuteImageGenerator", "所有网络源失败，生成本地可爱卡通图片")
                generateSimpleDrawing(level)
            } catch (e: Exception) {
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 检查本地是否有图片
    private fun hasLocalImage(level: Int): Boolean {
        val imageFile = File(imageDir, "cute_level_$level.png")
        return imageFile.exists()
    }

    // 获取本地图片
    private fun getLocalImage(level: Int): Bitmap? {
        return try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            if (imageFile.exists()) {
                BitmapFactory.decodeFile(imageFile.absolutePath)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    // 下载图片并永久保存
    private suspend fun downloadAndSaveImage(imageUrl: String, level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val bitmap = downloadImageFromUrl(imageUrl)
                if (bitmap != null) {
                    // 永久保存到本地
                    saveBitmapToLocal(bitmap, level)
                    bitmap
                } else {
                    null
                }
            } catch (e: Exception) {
                null
            }
        }
    }

    // 永久保存图片到本地
    private fun saveBitmapToLocal(bitmap: Bitmap, level: Int) {
        try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            val outputStream = FileOutputStream(imageFile)
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream) // 最高质量保存
            outputStream.close()
        } catch (e: Exception) {
            // 保存失败不影响主要功能
        }
    }

    // 检查关卡是否下载失败
    private fun isLevelFailed(level: Int): Boolean {
        return try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",")
                failedLevels.contains(level.toString())
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }

    // 标记关卡为下载失败
    private fun markLevelAsFailed(level: Int) {
        try {
            val failedLevels = if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText().split(",").toMutableSet()
            } else {
                mutableSetOf()
            }

            failedLevels.add(level.toString())
            failedDownloadsFile.writeText(failedLevels.joinToString(","))
        } catch (e: Exception) {
            // 记录失败不影响主要功能
        }
    }

    // 清理所有本地图片和失败记录（重置功能）
    fun clearAllData() {
        try {
            // 删除所有本地图片
            if (imageDir.exists()) {
                imageDir.listFiles()?.forEach { file ->
                    if (file.name.startsWith("cute_level_")) {
                        file.delete()
                    }
                }
            }

            // 删除失败记录
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.delete()
            }
        } catch (e: Exception) {
            // 清理失败不影响主要功能
        }
    }

    // 重新尝试失败的关卡
    fun retryFailedLevel(level: Int) {
        try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",").toMutableSet()
                failedLevels.remove(level.toString())

                if (failedLevels.isEmpty()) {
                    failedDownloadsFile.delete()
                } else {
                    failedDownloadsFile.writeText(failedLevels.joinToString(","))
                }
            }
        } catch (e: Exception) {
            // 重试失败不影响主要功能
        }
    }

    // 检查本地存储大小
    fun getLocalStorageSize(): Long {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()?.sumOf { it.length() } ?: 0L
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }

    // 获取已下载的关卡列表
    fun getDownloadedLevels(): List<Int> {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()
                    ?.filter { it.name.startsWith("cute_level_") && it.name.endsWith(".png") }
                    ?.mapNotNull { file ->
                        val levelStr = file.name.removePrefix("cute_level_").removeSuffix(".png")
                        levelStr.toIntOrNull()
                    }
                    ?.sorted() ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    // 获取失败的关卡列表
    fun getFailedLevels(): List<Int> {
        return try {
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText()
                    .split(",")
                    .mapNotNull { it.toIntOrNull() }
                    .sorted()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    




    // 从URL下载图片
    private suspend fun downloadImageFromUrl(imageUrl: String): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                Log.d("CuteImageGenerator", "开始下载图片: $imageUrl")
                val url = URL(imageUrl)
                val connection = url.openConnection() as HttpURLConnection

                // 设置请求头，模拟浏览器
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                connection.setRequestProperty("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                connection.setRequestProperty("Referer", "https://image.baidu.com/")

                connection.connectTimeout = 15000
                connection.readTimeout = 30000
                connection.doInput = true
                connection.instanceFollowRedirects = true

                val responseCode = connection.responseCode
                Log.d("CuteImageGenerator", "响应码: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val inputStream: InputStream = connection.inputStream
                    val bitmap = BitmapFactory.decodeStream(inputStream)
                    inputStream.close()
                    connection.disconnect()

                    if (bitmap != null) {
                        Log.d("CuteImageGenerator", "图片下载成功，尺寸: ${bitmap.width}x${bitmap.height}")
                    } else {
                        Log.e("CuteImageGenerator", "图片解码失败")
                    }

                    bitmap
                } else {
                    Log.e("CuteImageGenerator", "HTTP错误: $responseCode")
                    connection.disconnect()
                    null
                }
            } catch (e: Exception) {
                Log.e("CuteImageGenerator", "下载图片异常: $imageUrl", e)
                null
            }
        }
    }
    
    // 调整图片大小 - 保持高清质量
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        // 如果原图已经是目标尺寸或更大，保持原图质量
        if (bitmap.width >= targetWidth && bitmap.height >= targetHeight) {
            // 计算缩放比例，保持宽高比
            val scaleX = targetWidth.toFloat() / bitmap.width
            val scaleY = targetHeight.toFloat() / bitmap.height
            val scale = maxOf(scaleX, scaleY) // 使用较大的缩放比例确保填满

            val scaledWidth = (bitmap.width * scale).toInt()
            val scaledHeight = (bitmap.height * scale).toInt()

            // 使用高质量缩放
            val scaledBitmap = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true)

            // 如果缩放后的图片比目标尺寸大，进行居中裁剪
            return if (scaledWidth > targetWidth || scaledHeight > targetHeight) {
                val x = maxOf(0, (scaledWidth - targetWidth) / 2)
                val y = maxOf(0, (scaledHeight - targetHeight) / 2)
                Bitmap.createBitmap(scaledBitmap, x, y,
                    minOf(targetWidth, scaledWidth),
                    minOf(targetHeight, scaledHeight))
            } else {
                scaledBitmap
            }
        } else {
            // 原图太小，直接缩放
            return Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
        }
    }

    // 生成简笔画风格的可爱图片
    private fun generateSimpleDrawing(level: Int): Bitmap? {
        return try {
            val width = 800
            val height = 800
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            val paint = Paint(Paint.ANTI_ALIAS_FLAG)

            // 可爱的渐变背景
            val bgColors = listOf(
                Color.parseColor("#FFE4E1"), // 粉色
                Color.parseColor("#E6F3FF"), // 浅蓝
                Color.parseColor("#F0FFF0"), // 浅绿
                Color.parseColor("#FFF8DC"), // 浅黄
                Color.parseColor("#F5F0FF"), // 浅紫
                Color.parseColor("#FFE4CC"), // 浅橙
                Color.parseColor("#E0FFFF"), // 浅青
                Color.parseColor("#FFF0F5")  // 浅玫瑰
            )
            canvas.drawColor(bgColors[level % bgColors.size])

            // 可爱卡通风格设置
            paint.style = Paint.Style.FILL
            paint.strokeWidth = 4f
            paint.strokeCap = Paint.Cap.ROUND

            val centerX = width / 2f
            val centerY = height / 2f

            // 根据关卡生成不同的可爱卡通图案
            when (level % 10) {
                0 -> drawCuteCat(canvas, paint, centerX, centerY)
                1 -> drawCuteDog(canvas, paint, centerX, centerY)
                2 -> drawCuteBear(canvas, paint, centerX, centerY)
                3 -> drawCuteRabbit(canvas, paint, centerX, centerY)
                4 -> drawCuteFlower(canvas, paint, centerX, centerY)
                5 -> drawCuteSun(canvas, paint, centerX, centerY)
                6 -> drawCuteHeart(canvas, paint, centerX, centerY)
                7 -> drawCuteHouse(canvas, paint, centerX, centerY)
                8 -> drawCuteCake(canvas, paint, centerX, centerY)
                else -> drawCuteSmile(canvas, paint, centerX, centerY)
            }

            // 保存到本地
            saveBitmapToLocal(bitmap, level)

            Log.d("CuteImageGenerator", "关卡 $level 可爱卡通图片生成成功")
            bitmap
        } catch (e: Exception) {
            Log.e("CuteImageGenerator", "生成可爱卡通图片失败", e)
            null
        }
    }

    // 可爱卡通绘制方法
    private fun drawCuteCat(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 头部 - 粉色
        paint.color = Color.parseColor("#FFB6C1")
        canvas.drawCircle(centerX, centerY, 120f, paint)

        // 耳朵 - 深粉色
        paint.color = Color.parseColor("#FF69B4")
        val path = Path()
        path.moveTo(centerX - 80f, centerY - 80f)
        path.lineTo(centerX - 40f, centerY - 120f)
        path.lineTo(centerX - 20f, centerY - 80f)
        path.close()
        canvas.drawPath(path, paint)

        path.reset()
        path.moveTo(centerX + 20f, centerY - 80f)
        path.lineTo(centerX + 40f, centerY - 120f)
        path.lineTo(centerX + 80f, centerY - 80f)
        path.close()
        canvas.drawPath(path, paint)

        // 眼睛 - 白色底
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 40f, centerY - 20f, 25f, paint)
        canvas.drawCircle(centerX + 40f, centerY - 20f, 25f, paint)

        // 眼珠 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 40f, centerY - 20f, 15f, paint)
        canvas.drawCircle(centerX + 40f, centerY - 20f, 15f, paint)

        // 眼睛高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 35f, centerY - 25f, 5f, paint)
        canvas.drawCircle(centerX + 45f, centerY - 25f, 5f, paint)

        // 鼻子 - 粉色三角形
        paint.color = Color.parseColor("#FF1493")
        val nosePath = Path()
        nosePath.moveTo(centerX, centerY + 10f)
        nosePath.lineTo(centerX - 10f, centerY + 30f)
        nosePath.lineTo(centerX + 10f, centerY + 30f)
        nosePath.close()
        canvas.drawPath(nosePath, paint)

        // 嘴巴 - 黑色线条
        paint.color = Color.BLACK
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 4f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 30f, centerY + 40f)
        mouthPath.quadTo(centerX, centerY + 30f, centerX + 30f, centerY + 40f)
        canvas.drawPath(mouthPath, paint)

        // 腮红 - 浅粉色
        paint.style = Paint.Style.FILL
        paint.color = Color.parseColor("#FFCCCB")
        canvas.drawCircle(centerX - 90f, centerY + 20f, 20f, paint)
        canvas.drawCircle(centerX + 90f, centerY + 20f, 20f, paint)
    }

    private fun drawCuteDog(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 头部 - 棕色
        paint.color = Color.parseColor("#DEB887")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 120f, paint)

        // 耳朵（下垂）- 深棕色
        paint.color = Color.parseColor("#8B4513")
        canvas.drawOval(centerX - 100f, centerY - 60f, centerX - 60f, centerY + 20f, paint)
        canvas.drawOval(centerX + 60f, centerY - 60f, centerX + 100f, centerY + 20f, paint)

        // 眼睛 - 白色底
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 40f, centerY - 20f, 25f, paint)
        canvas.drawCircle(centerX + 40f, centerY - 20f, 25f, paint)

        // 眼珠 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 40f, centerY - 20f, 15f, paint)
        canvas.drawCircle(centerX + 40f, centerY - 20f, 15f, paint)

        // 眼睛高光
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 35f, centerY - 25f, 5f, paint)
        canvas.drawCircle(centerX + 45f, centerY - 25f, 5f, paint)

        // 鼻子 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX, centerY + 20f, 12f, paint)

        // 嘴巴 - 黑色弧线
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 4f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 40f, centerY + 50f)
        mouthPath.quadTo(centerX, centerY + 35f, centerX + 40f, centerY + 50f)
        canvas.drawPath(mouthPath, paint)

        // 舌头 - 粉色
        paint.style = Paint.Style.FILL
        paint.color = Color.parseColor("#FF69B4")
        canvas.drawOval(centerX - 8f, centerY + 35f, centerX + 8f, centerY + 70f, paint)
    }

    private fun drawCuteBear(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 头部 - 棕色
        paint.color = Color.parseColor("#CD853F")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 120f, paint)

        // 耳朵（圆形）- 深棕色
        paint.color = Color.parseColor("#8B4513")
        canvas.drawCircle(centerX - 80f, centerY - 80f, 40f, paint)
        canvas.drawCircle(centerX + 80f, centerY - 80f, 40f, paint)

        // 耳朵内部 - 浅棕色
        paint.color = Color.parseColor("#DEB887")
        canvas.drawCircle(centerX - 80f, centerY - 80f, 25f, paint)
        canvas.drawCircle(centerX + 80f, centerY - 80f, 25f, paint)

        // 眼睛 - 白色底
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 40f, centerY - 20f, 25f, paint)
        canvas.drawCircle(centerX + 40f, centerY - 20f, 25f, paint)

        // 眼珠 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 40f, centerY - 20f, 15f, paint)
        canvas.drawCircle(centerX + 40f, centerY - 20f, 15f, paint)

        // 眼睛高光
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 35f, centerY - 25f, 5f, paint)
        canvas.drawCircle(centerX + 45f, centerY - 25f, 5f, paint)

        // 鼻子 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX, centerY + 20f, 12f, paint)

        // 嘴巴 - 黑色弧线
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 4f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 30f, centerY + 50f)
        mouthPath.quadTo(centerX, centerY + 60f, centerX + 30f, centerY + 50f)
        canvas.drawPath(mouthPath, paint)
    }

    private fun drawCuteRabbit(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 头部 - 白色
        paint.color = Color.WHITE
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 120f, paint)

        // 长耳朵 - 粉色
        paint.color = Color.parseColor("#FFB6C1")
        canvas.drawOval(centerX - 60f, centerY - 180f, centerX - 20f, centerY - 80f, paint)
        canvas.drawOval(centerX + 20f, centerY - 180f, centerX + 60f, centerY - 80f, paint)

        // 耳朵内部 - 浅粉色
        paint.color = Color.parseColor("#FFCCCB")
        canvas.drawOval(centerX - 50f, centerY - 160f, centerX - 30f, centerY - 100f, paint)
        canvas.drawOval(centerX + 30f, centerY - 160f, centerX + 50f, centerY - 100f, paint)

        // 眼睛 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 40f, centerY - 20f, 15f, paint)
        canvas.drawCircle(centerX + 40f, centerY - 20f, 15f, paint)

        // 眼睛高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 35f, centerY - 25f, 5f, paint)
        canvas.drawCircle(centerX + 45f, centerY - 25f, 5f, paint)

        // 鼻子 - 粉色三角形
        paint.color = Color.parseColor("#FF69B4")
        val nosePath = Path()
        nosePath.moveTo(centerX, centerY + 10f)
        nosePath.lineTo(centerX - 10f, centerY + 30f)
        nosePath.lineTo(centerX + 10f, centerY + 30f)
        nosePath.close()
        canvas.drawPath(nosePath, paint)

        // 嘴巴 - 黑色
        paint.color = Color.BLACK
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 3f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 20f, centerY + 40f)
        mouthPath.quadTo(centerX, centerY + 30f, centerX + 20f, centerY + 40f)
        canvas.drawPath(mouthPath, paint)
    }

    private fun drawCuteFlower(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 花心 - 黄色
        paint.color = Color.parseColor("#FFD700")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 30f, paint)

        // 花瓣 - 粉色
        paint.color = Color.parseColor("#FF69B4")
        canvas.drawCircle(centerX, centerY - 60f, 25f, paint)
        canvas.drawCircle(centerX + 52f, centerY - 30f, 25f, paint)
        canvas.drawCircle(centerX + 52f, centerY + 30f, 25f, paint)
        canvas.drawCircle(centerX, centerY + 60f, 25f, paint)
        canvas.drawCircle(centerX - 52f, centerY + 30f, 25f, paint)
        canvas.drawCircle(centerX - 52f, centerY - 30f, 25f, paint)

        // 茎 - 绿色
        paint.color = Color.parseColor("#32CD32")
        paint.strokeWidth = 8f
        paint.style = Paint.Style.STROKE
        canvas.drawLine(centerX, centerY + 60f, centerX, centerY + 200f, paint)

        // 叶子 - 绿色
        paint.style = Paint.Style.FILL
        canvas.drawOval(centerX + 10f, centerY + 120f, centerX + 50f, centerY + 160f, paint)
        canvas.drawOval(centerX - 50f, centerY + 140f, centerX - 10f, centerY + 180f, paint)
    }



    private fun drawCuteSun(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 太阳主体 - 金黄色
        paint.color = Color.parseColor("#FFD700")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 100f, paint)

        // 阳光射线 - 橙色
        paint.color = Color.parseColor("#FFA500")
        paint.strokeWidth = 8f
        paint.style = Paint.Style.STROKE
        paint.strokeCap = Paint.Cap.ROUND

        for (i in 0..11) {
            val angle = i * Math.PI / 6
            val startX = centerX + (120f * cos(angle)).toFloat()
            val startY = centerY + (120f * sin(angle)).toFloat()
            val endX = centerX + (160f * cos(angle)).toFloat()
            val endY = centerY + (160f * sin(angle)).toFloat()
            canvas.drawLine(startX, startY, endX, endY, paint)
        }

        // 眼睛 - 黑色
        paint.style = Paint.Style.FILL
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 30f, centerY - 25f, 12f, paint)
        canvas.drawCircle(centerX + 30f, centerY - 25f, 12f, paint)

        // 眼睛高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 25f, centerY - 30f, 4f, paint)
        canvas.drawCircle(centerX + 35f, centerY - 30f, 4f, paint)

        // 腮红 - 粉色
        paint.color = Color.parseColor("#FFB6C1")
        canvas.drawCircle(centerX - 70f, centerY + 10f, 15f, paint)
        canvas.drawCircle(centerX + 70f, centerY + 10f, 15f, paint)

        // 嘴巴 - 红色弧线
        paint.color = Color.parseColor("#FF4500")
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 6f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 40f, centerY + 20f)
        mouthPath.quadTo(centerX, centerY + 50f, centerX + 40f, centerY + 20f)
        canvas.drawPath(mouthPath, paint)
    }

    private fun drawCuteHeart(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 爱心 - 粉色
        paint.color = Color.parseColor("#FF69B4")
        paint.style = Paint.Style.FILL

        val path = Path()
        path.moveTo(centerX, centerY + 80f)
        path.cubicTo(centerX - 80f, centerY - 40f, centerX - 120f, centerY - 120f, centerX - 40f, centerY - 120f)
        path.cubicTo(centerX - 20f, centerY - 140f, centerX + 20f, centerY - 140f, centerX + 40f, centerY - 120f)
        path.cubicTo(centerX + 120f, centerY - 120f, centerX + 80f, centerY - 40f, centerX, centerY + 80f)
        canvas.drawPath(path, paint)

        // 爱心高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 30f, centerY - 60f, 15f, paint)
        canvas.drawCircle(centerX + 30f, centerY - 60f, 10f, paint)
    }

    private fun drawCuteHouse(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 房子主体 - 黄色
        paint.color = Color.parseColor("#FFFF99")
        paint.style = Paint.Style.FILL
        canvas.drawRect(centerX - 80f, centerY, centerX + 80f, centerY + 120f, paint)

        // 屋顶 - 红色
        paint.color = Color.parseColor("#FF6347")
        val roofPath = Path()
        roofPath.moveTo(centerX - 100f, centerY)
        roofPath.lineTo(centerX, centerY - 80f)
        roofPath.lineTo(centerX + 100f, centerY)
        roofPath.close()
        canvas.drawPath(roofPath, paint)

        // 门 - 棕色
        paint.color = Color.parseColor("#8B4513")
        canvas.drawRect(centerX - 20f, centerY + 60f, centerX + 20f, centerY + 120f, paint)

        // 门把手 - 金色
        paint.color = Color.parseColor("#FFD700")
        canvas.drawCircle(centerX + 15f, centerY + 90f, 3f, paint)

        // 窗户 - 蓝色
        paint.color = Color.parseColor("#87CEEB")
        canvas.drawRect(centerX - 60f, centerY + 30f, centerX - 30f, centerY + 60f, paint)
        canvas.drawRect(centerX + 30f, centerY + 30f, centerX + 60f, centerY + 60f, paint)

        // 窗框 - 白色
        paint.color = Color.WHITE
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 3f
        canvas.drawRect(centerX - 60f, centerY + 30f, centerX - 30f, centerY + 60f, paint)
        canvas.drawRect(centerX + 30f, centerY + 30f, centerX + 60f, centerY + 60f, paint)

        // 窗户十字
        canvas.drawLine(centerX - 45f, centerY + 30f, centerX - 45f, centerY + 60f, paint)
        canvas.drawLine(centerX - 60f, centerY + 45f, centerX - 30f, centerY + 45f, paint)
        canvas.drawLine(centerX + 45f, centerY + 30f, centerX + 45f, centerY + 60f, paint)
        canvas.drawLine(centerX + 30f, centerY + 45f, centerX + 60f, centerY + 45f, paint)
    }





    private fun drawCuteCake(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 蛋糕底层 - 粉色
        paint.color = Color.parseColor("#FFB6C1")
        paint.style = Paint.Style.FILL
        canvas.drawRect(centerX - 80f, centerY + 40f, centerX + 80f, centerY + 120f, paint)

        // 蛋糕中层 - 白色
        paint.color = Color.WHITE
        canvas.drawRect(centerX - 60f, centerY - 20f, centerX + 60f, centerY + 40f, paint)

        // 蛋糕顶层 - 黄色
        paint.color = Color.parseColor("#FFFF99")
        canvas.drawRect(centerX - 40f, centerY - 60f, centerX + 40f, centerY - 20f, paint)

        // 蜡烛 - 红色
        paint.color = Color.parseColor("#FF4500")
        canvas.drawRect(centerX - 3f, centerY - 100f, centerX + 3f, centerY - 60f, paint)

        // 火焰 - 橙色
        paint.color = Color.parseColor("#FFA500")
        canvas.drawOval(centerX - 5f, centerY - 110f, centerX + 5f, centerY - 100f, paint)

        // 装饰樱桃 - 红色
        paint.color = Color.parseColor("#DC143C")
        canvas.drawCircle(centerX - 20f, centerY - 40f, 8f, paint)
        canvas.drawCircle(centerX + 20f, centerY - 40f, 8f, paint)
    }

    private fun drawCuteSmile(canvas: Canvas, paint: Paint, centerX: Float, centerY: Float) {
        // 笑脸圆形 - 黄色
        paint.color = Color.parseColor("#FFD700")
        paint.style = Paint.Style.FILL
        canvas.drawCircle(centerX, centerY, 100f, paint)

        // 眼睛 - 黑色
        paint.color = Color.BLACK
        canvas.drawCircle(centerX - 35f, centerY - 25f, 15f, paint)
        canvas.drawCircle(centerX + 35f, centerY - 25f, 15f, paint)

        // 眼睛高光 - 白色
        paint.color = Color.WHITE
        canvas.drawCircle(centerX - 30f, centerY - 30f, 5f, paint)
        canvas.drawCircle(centerX + 40f, centerY - 30f, 5f, paint)

        // 腮红 - 粉色
        paint.color = Color.parseColor("#FFB6C1")
        canvas.drawCircle(centerX - 70f, centerY + 10f, 12f, paint)
        canvas.drawCircle(centerX + 70f, centerY + 10f, 12f, paint)

        // 嘴巴 - 红色
        paint.color = Color.parseColor("#FF4500")
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 6f
        val mouthPath = Path()
        mouthPath.moveTo(centerX - 50f, centerY + 20f)
        mouthPath.quadTo(centerX, centerY + 60f, centerX + 50f, centerY + 20f)
        canvas.drawPath(mouthPath, paint)
    }


}
