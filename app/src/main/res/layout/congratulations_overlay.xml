<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:clickable="true"
    android:focusable="true">

    <!-- 恭喜动画容器 -->
    <LinearLayout
        android:id="@+id/congratulationsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="32dp"
        android:background="@drawable/congratulations_background"
        android:orientation="vertical"
        android:padding="32dp"
        android:gravity="center">

        <!-- 恭喜标题 -->
        <TextView
            android:id="@+id/congratulationsTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🎉 恭喜完成！ 🎉"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="#FF6B35"
            android:layout_marginBottom="16dp"
            android:gravity="center" />

        <!-- 关卡信息 -->
        <TextView
            android:id="@+id/levelCompletedText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="第 1 关完成！"
            android:textSize="20sp"
            android:textColor="#4A90E2"
            android:layout_marginBottom="8dp"
            android:gravity="center" />

        <!-- 角色名称 -->
        <TextView
            android:id="@+id/characterNameText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="艾莎公主 👸"
            android:textSize="18sp"
            android:textColor="#9B59B6"
            android:layout_marginBottom="24dp"
            android:gravity="center" />

        <!-- 星星评分 -->
        <LinearLayout
            android:id="@+id/starsContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <TextView
                android:id="@+id/star1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="⭐"
                android:textSize="32sp"
                android:layout_marginHorizontal="4dp" />

            <TextView
                android:id="@+id/star2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="⭐"
                android:textSize="32sp"
                android:layout_marginHorizontal="4dp" />

            <TextView
                android:id="@+id/star3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="⭐"
                android:textSize="32sp"
                android:layout_marginHorizontal="4dp" />

        </LinearLayout>

        <!-- 鼓励文字 -->
        <TextView
            android:id="@+id/encouragementText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="太棒了！你真厉害！ 💖"
            android:textSize="16sp"
            android:textColor="#E74C3C"
            android:layout_marginBottom="32dp"
            android:gravity="center" />

        <!-- 按钮容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <!-- 第一行按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginBottom="12dp">

                <!-- 返回主菜单按钮 -->
                <Button
                    android:id="@+id/backToMenuButton"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="🏠 主菜单"
                    android:textSize="16sp"
                    android:textColor="#FFFFFF"
                    android:background="@drawable/button_secondary"
                    android:elevation="4dp" />

                <!-- 下一关按钮 -->
                <Button
                    android:id="@+id/nextLevelButton"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="🚀 下一关"
                    android:textSize="16sp"
                    android:textColor="#FFFFFF"
                    android:background="@drawable/button_primary"
                    android:elevation="4dp" />

            </LinearLayout>

            <!-- 第二行：关闭按钮 -->
            <Button
                android:id="@+id/closeButton"
                android:layout_width="wrap_content"
                android:layout_height="48dp"
                android:paddingHorizontal="32dp"
                android:text="❌ 关闭"
                android:textSize="14sp"
                android:textColor="#666666"
                android:background="@drawable/button_close"
                android:elevation="2dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 飘落的星星动画 -->
    <ImageView
        android:id="@+id/fallingStars1"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="top|start"
        android:layout_marginStart="50dp"
        android:layout_marginTop="100dp"
        android:src="@drawable/star_icon"
        android:alpha="0" />

    <ImageView
        android:id="@+id/fallingStars2"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="top|end"
        android:layout_marginEnd="80dp"
        android:layout_marginTop="150dp"
        android:src="@drawable/star_icon"
        android:alpha="0" />

    <ImageView
        android:id="@+id/fallingStars3"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="top|start"
        android:layout_marginStart="120dp"
        android:layout_marginTop="80dp"
        android:src="@drawable/star_icon"
        android:alpha="0" />

    <ImageView
        android:id="@+id/fallingStars4"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="top|end"
        android:layout_marginEnd="150dp"
        android:layout_marginTop="120dp"
        android:src="@drawable/star_icon"
        android:alpha="0" />

</FrameLayout>
