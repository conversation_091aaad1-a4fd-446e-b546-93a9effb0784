<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="200dp"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp"
        android:gravity="center">

        <FrameLayout
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginBottom="8dp">

            <ImageView
                android:id="@+id/iv_level_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@drawable/image_border" />

            <ImageView
                android:id="@+id/iv_lock_icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_lock"
                android:visibility="gone" />

        </FrameLayout>

        <TextView
            android:id="@+id/tv_level_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="第1关"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text_color"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tv_level_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="简单 3x3"
            android:textSize="14sp"
            android:textColor="@color/secondary_text_color"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tv_piece_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="9块"
            android:textSize="12sp"
            android:textColor="@color/secondary_text_color"
            android:background="@drawable/badge_background"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
