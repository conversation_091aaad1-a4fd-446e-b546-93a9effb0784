# 🎯 全局难度系统实现说明

## 📋 功能概述

实现了全局难度设置系统，将难度选择移到首页，删除了开始游戏按钮，所有拼图都根据全局难度设置和图片比例自适应网格大小。

## 🏗️ 系统架构

### 1. 核心组件

#### GlobalDifficultyManager (全局难度管理器)
- 📱 **单例模式**：管理应用级别的难度设置
- 💾 **持久化存储**：使用SharedPreferences保存用户选择
- 🔄 **自动加载**：应用启动时自动加载保存的难度设置
- 📊 **统计信息**：提供详细的难度统计和网格计算

#### GlobalDifficultySelectActivity (全局难度选择界面)
- 🎨 **精美UI**：卡片式设计，颜色区分难度等级
- ✅ **选中状态**：清晰显示当前选择的难度
- 📊 **详细信息**：显示每个难度的网格效果
- 🔄 **实时更新**：选择后立即更新全局设置

### 2. 界面改进

#### MainActivity (首页)
**更新前**：
```
🎮 拼图游戏
[开始游戏] [选择关卡] [退出游戏]
```

**更新后**：
```
🎮 拼图游戏
┌─────────────────┐
│ 🎯 游戏难度     │
│ 中等 (3×3 格子) │
│ [更改难度]      │
└─────────────────┘
[🎮 选择关卡] [退出游戏]
```

## 🎮 新的游戏流程

### 更新前的流程
```
首页 → 选择关卡 → 难度选择 → 开始拼图
```

### 更新后的流程
```
首页 (设置全局难度) → 选择关卡 → 开始拼图
```

## 🔧 技术实现

### 1. 全局难度管理
```kotlin
object GlobalDifficultyManager {
    // 初始化
    fun initialize(context: Context)
    
    // 设置全局难度
    fun setGlobalDifficulty(difficulty: DifficultyLevel)
    
    // 获取当前难度
    fun getCurrentDifficulty(): DifficultyLevel
    
    // 根据图片计算网格
    fun calculateGridForImage(imageWidth: Int, imageHeight: Int): Pair<Int, Int>
    
    // 获取正方形网格
    fun getSquareGrid(): Pair<Int, Int>
}
```

### 2. 自适应网格计算
```kotlin
// 智能网格计算逻辑
fun calculateGridForImage(imageWidth: Int, imageHeight: Int): Pair<Int, Int> {
    val aspectRatio = imageWidth.toFloat() / imageHeight.toFloat()
    val baseSize = currentDifficulty.baseGridSize
    
    return when {
        // 正方形图片 (比例 0.8-1.25)
        aspectRatio >= 0.8f && aspectRatio <= 1.25f -> 
            Pair(baseSize, baseSize)
        
        // 横向图片 (宽 > 高)
        aspectRatio > 1.25f -> 
            Pair(baseSize + 1, baseSize)
        
        // 纵向图片 (高 > 宽)
        aspectRatio < 0.8f -> 
            Pair(baseSize, baseSize + 1)
        
        else -> Pair(baseSize, baseSize)
    }
}
```

### 3. PuzzleView 自动适配
```kotlin
class PuzzleView {
    // 使用全局难度
    fun useGlobalDifficulty() {
        currentDifficulty = GlobalDifficultyManager.getCurrentDifficulty()
    }
    
    // 自动计算网格
    private fun calculateDynamicGrid(imageWidth: Int, imageHeight: Int) {
        useGlobalDifficulty()
        val gridDimensions = GlobalDifficultyManager.calculateGridForImage(imageWidth, imageHeight)
        gridCols = gridDimensions.first
        gridRows = gridDimensions.second
    }
}
```

## 📊 网格自适应示例

### 中等难度 (基础 3×3)
```
图片类型        网格大小    拼图块数
正方形图片      3×3        9块
横向图片        4×3        12块
纵向图片        3×4        12块
```

### 简单难度 (基础 2×2)
```
图片类型        网格大小    拼图块数
正方形图片      2×2        4块
横向图片        3×2        6块
纵向图片        2×3        6块
```

### 困难难度 (基础 4×4)
```
图片类型        网格大小    拼图块数
正方形图片      4×4        16块
横向图片        5×4        20块
纵向图片        4×5        20块
```

## 🌟 支持的主题

### 全主题自适应
- ✅ **可爱主题**：正方形网格，根据全局难度调整
- ✅ **语音搜图主题**：根据搜索图片比例和全局难度自适应
- ✅ **拍照主题**：根据照片比例和全局难度自适应
- ✅ **其他主题**：统一使用全局难度系统

### 比例适配逻辑
```
图片比例判断：
- 0.8 ≤ 宽高比 ≤ 1.25  → 正方形网格
- 宽高比 > 1.25         → 横向网格 (列数+1)
- 宽高比 < 0.8          → 纵向网格 (行数+1)
```

## 💾 数据持久化

### SharedPreferences 存储
```kotlin
private const val PREFS_NAME = "puzzle_game_settings"
private const val KEY_GLOBAL_DIFFICULTY = "global_difficulty"

// 保存难度设置
private fun saveDifficulty() {
    sharedPreferences?.edit()?.apply {
        putString(KEY_GLOBAL_DIFFICULTY, currentDifficulty.name)
        apply()
    }
}

// 加载难度设置
private fun loadSavedDifficulty() {
    val savedDifficultyName = sharedPreferences?.getString(KEY_GLOBAL_DIFFICULTY, null)
    currentDifficulty = DifficultyLevel.fromName(savedDifficultyName ?: "MEDIUM")
}
```

## 🎨 用户体验

### 1. 首页难度显示
- 🎯 **清晰标识**：显示当前全局难度
- 📊 **基础信息**：显示基础网格大小
- 🔄 **快速更改**：一键进入难度设置

### 2. 难度选择界面
- 🎨 **视觉区分**：简单(绿色)、中等(蓝色)、困难(红色)
- ✅ **选中状态**：清晰显示当前选择
- 📊 **详细预览**：显示各种图片比例的网格效果
- 💡 **智能提示**：说明自适应网格的工作原理

### 3. 游戏内体验
- 🔄 **无缝体验**：不再需要每次选择难度
- 📐 **智能适配**：网格自动适应图片比例
- 📊 **信息显示**：游戏内显示实际网格和难度

## 🚀 性能优化

### 1. 单例模式
- 📱 **内存效率**：全局唯一实例
- ⚡ **快速访问**：避免重复创建对象

### 2. 智能缓存
- 💾 **设置缓存**：难度设置持久化存储
- 🔄 **自动加载**：应用启动时自动恢复设置

### 3. 计算优化
- 📐 **高效算法**：简单的比例计算
- 🎯 **合理范围**：网格大小控制在合理范围内

## 📱 兼容性保证

### 向后兼容
- ✅ **默认设置**：首次使用时使用中等难度
- ✅ **平滑迁移**：现有功能无缝过渡
- ✅ **错误处理**：异常时自动使用默认难度

### 跨主题支持
- ✅ **统一接口**：所有主题使用相同的难度系统
- ✅ **参数传递**：保持原有的特殊参数传递
- ✅ **功能完整**：所有原有功能正常工作

这个全局难度系统为用户提供了更简洁、更智能的拼图体验，所有拼图都会根据用户设置的难度和图片比例自动调整网格大小！🎉
