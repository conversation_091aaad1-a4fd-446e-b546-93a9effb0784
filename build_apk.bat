@echo off
chcp 65001 >nul
echo 🚀 开始编译公主拼图游戏APK...
echo.

REM 获取当前日期
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "datestamp=%YYYY%%MM%%DD%"

echo 📅 编译日期: %datestamp%
echo.

REM 检查图片文件
echo 🔍 检查拼图图片文件...
set "missing_files=0"

if not exist "app\src\main\res\drawable\puzzle_image_1.png" (
    echo ❌ puzzle_image_1.png 缺失
    set /a missing_files+=1
)
if not exist "app\src\main\res\drawable\puzzle_image_2.png" (
    echo ❌ puzzle_image_2.png 缺失
    set /a missing_files+=1
)
if not exist "app\src\main\res\drawable\puzzle_image_3.png" (
    echo ❌ puzzle_image_3.png 缺失
    set /a missing_files+=1
)
if not exist "app\src\main\res\drawable\puzzle_image_4.png" (
    echo ❌ puzzle_image_4.png 缺失
    set /a missing_files+=1
)
if not exist "app\src\main\res\drawable\puzzle_image_5.png" (
    echo ❌ puzzle_image_5.png 缺失
    set /a missing_files+=1
)

if %missing_files% gtr 0 (
    echo.
    echo ⚠️  发现 %missing_files% 个图片文件缺失！
    echo 请先下载所有拼图图片再编译APK
    pause
    exit /b 1
)

echo ✅ 所有图片文件检查完成！
echo.

REM 清理之前的构建
echo 🧹 清理之前的构建文件...
call gradlew.bat clean
if errorlevel 1 (
    echo ❌ 清理失败！
    pause
    exit /b 1
)

echo.
echo 🔨 开始编译APK...
call gradlew.bat assembleRelease
if errorlevel 1 (
    echo ❌ 编译失败！
    echo.
    echo 💡 尝试编译Debug版本...
    call gradlew.bat assembleDebug
    if errorlevel 1 (
        echo ❌ Debug编译也失败！
        pause
        exit /b 1
    )
    set "build_type=debug"
    set "apk_path=app\build\outputs\apk\debug\app-debug.apk"
) else (
    set "build_type=release"
    set "apk_path=app\build\outputs\apk\release\app-release-unsigned.apk"
)

echo.
echo ✅ APK编译成功！
echo.

REM 检查APK文件是否存在
if not exist "%apk_path%" (
    echo ❌ 找不到编译的APK文件: %apk_path%
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "release" mkdir release

REM 生成新的APK文件名
set "new_apk_name=%datestamp%_PrincessPuzzle_%build_type%.apk"
set "output_path=release\%new_apk_name%"

REM 复制并重命名APK
copy "%apk_path%" "%output_path%"
if errorlevel 1 (
    echo ❌ 复制APK失败！
    pause
    exit /b 1
)

echo 🎉 APK编译完成！
echo.
echo 📱 APK信息:
echo    文件名: %new_apk_name%
echo    路径: %output_path%
echo    类型: %build_type%
echo    日期: %datestamp%
echo.

REM 显示文件大小
for %%A in ("%output_path%") do (
    set "size=%%~zA"
    set /a size_mb=!size!/1024/1024
    echo    大小: !size_mb! MB
)

echo.
echo 🎯 安装说明:
echo 1. 将APK文件传输到Android设备
echo 2. 在设备上启用"未知来源"安装
echo 3. 点击APK文件进行安装
echo 4. 享受公主拼图游戏！
echo.

REM 询问是否打开文件夹
set /p "open_folder=是否打开APK所在文件夹? (y/n): "
if /i "%open_folder%"=="y" (
    explorer release
)

echo.
echo ✨ 编译完成！APK已保存为: %new_apk_name%
pause
