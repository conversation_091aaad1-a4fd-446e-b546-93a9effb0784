package com.ai.game_2

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.speech.RecognizerIntent
import android.view.View
import android.view.Window
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import java.util.*

class VoiceInputDialog(
    context: Context,
    private val onResult: (String?) -> Unit
) : Dialog(context) {

    private lateinit var voiceWaveView: VoiceWaveView
    private lateinit var statusText: TextView
    private lateinit var cancelButton: Button
    private var activity: AppCompatActivity? = null

    companion object {
        private const val REQUEST_SPEECH_INPUT = 100
    }

    init {
        if (context is AppCompatActivity) {
            activity = context
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setContentView(R.layout.dialog_voice_input)

        initializeViews()
        setupClickListeners()
        startVoiceRecognition()
    }

    private fun initializeViews() {
        voiceWaveView = findViewById(R.id.voice_wave_view)
        statusText = findViewById(R.id.tv_status)
        cancelButton = findViewById(R.id.btn_cancel)

        // 开始波形动画
        voiceWaveView.visibility = View.VISIBLE
        voiceWaveView.startRecording()
        statusText.text = "请说话..."
    }

    private fun setupClickListeners() {
        cancelButton.setOnClickListener {
            stopRecording()
            dismiss()
            onResult(null)
        }

        // 点击外部区域取消
        setCanceledOnTouchOutside(true)
        setOnCancelListener {
            stopRecording()
            onResult(null)
        }
    }

    private fun startVoiceRecognition() {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
            putExtra(RecognizerIntent.EXTRA_PROMPT, "请说出你想要搜索的图片内容...")
            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
        }

        try {
            activity?.startActivityForResult(intent, REQUEST_SPEECH_INPUT)
        } catch (e: Exception) {
            stopRecording()
            statusText.text = "语音识别不可用"
            // 延迟关闭对话框
            voiceWaveView.postDelayed({
                dismiss()
                onResult(null)
            }, 2000)
        }
    }

    fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == REQUEST_SPEECH_INPUT) {
            stopRecording()
            
            if (resultCode == AppCompatActivity.RESULT_OK) {
                val result = data?.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS)
                val spokenText = result?.firstOrNull()
                
                if (!spokenText.isNullOrEmpty()) {
                    statusText.text = "识别成功: $spokenText"
                    // 延迟关闭，让用户看到结果
                    voiceWaveView.postDelayed({
                        dismiss()
                        onResult(spokenText)
                    }, 1000)
                } else {
                    statusText.text = "未识别到语音"
                    voiceWaveView.postDelayed({
                        dismiss()
                        onResult(null)
                    }, 2000)
                }
            } else {
                statusText.text = "语音识别取消"
                voiceWaveView.postDelayed({
                    dismiss()
                    onResult(null)
                }, 1500)
            }
        }
    }

    private fun stopRecording() {
        voiceWaveView.stopRecording()
    }

    override fun onBackPressed() {
        stopRecording()
        super.onBackPressed()
        onResult(null)
    }
}
