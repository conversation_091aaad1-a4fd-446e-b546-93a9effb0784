package com.ai.game_2

import android.os.Bundle
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class TestActivity : AppCompatActivity() {

    private lateinit var imageView: ImageView
    private lateinit var statusText: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            // 创建一个简单的布局来测试CuteImageGenerator
            createTestLayout()

            Toast.makeText(this, "测试Activity启动成功！", Toast.LENGTH_SHORT).show()

            // 测试CuteImageGenerator
            testCuteImageGenerator()

        } catch (e: Exception) {
            Toast.makeText(this, "错误: ${e.message}", Toast.LENGTH_LONG).show()
            e.printStackTrace()
        }
    }

    private fun createTestLayout() {
        val scrollView = ScrollView(this)
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        // 标题
        val titleText = TextView(this).apply {
            text = "🎀 可爱简笔画下载测试"
            textSize = 20f
            setTypeface(null, android.graphics.Typeface.BOLD)
            gravity = android.view.Gravity.CENTER
        }
        layout.addView(titleText)

        // 状态文本
        statusText = TextView(this).apply {
            text = "准备测试从百度/必应下载可爱简笔画..."
            textSize = 14f
            setPadding(0, 30, 0, 20)
        }
        layout.addView(statusText)

        // 图片显示
        imageView = ImageView(this).apply {
            layoutParams = LinearLayout.LayoutParams(400, 400).apply {
                topMargin = 20
                gravity = android.view.Gravity.CENTER_HORIZONTAL
            }
            scaleType = ImageView.ScaleType.CENTER_CROP
            setBackgroundColor(android.graphics.Color.LTGRAY)
        }
        layout.addView(imageView)

        // 按钮布局
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                topMargin = 30
            }
        }

        val testButton = Button(this).apply {
            text = "🔄 重新下载"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                rightMargin = 10
            }
            setOnClickListener { testCuteImageGenerator() }
        }
        buttonLayout.addView(testButton)

        val clearCacheButton = Button(this).apply {
            text = "🗑️ 清除缓存"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                leftMargin = 10
            }
            setOnClickListener { clearCache() }
        }
        buttonLayout.addView(clearCacheButton)

        layout.addView(buttonLayout)

        // 说明文本
        val infoText = TextView(this).apply {
            text = """
                📝 测试说明：
                • 点击"重新下载"测试从网络下载可爱简笔画
                • 支持百度和必应图片搜索
                • 下载失败时自动使用本地绘制图片
                • 图片会自动缓存到本地
                • 每次测试随机选择不同主题
            """.trimIndent()
            textSize = 12f
            setPadding(0, 30, 0, 0)
            setTextColor(android.graphics.Color.GRAY)
        }
        layout.addView(infoText)

        scrollView.addView(layout)
        setContentView(scrollView)
    }

    private fun clearCache() {
        try {
            val cacheFiles = cacheDir.listFiles { file ->
                file.name.startsWith("cute_image_") && file.name.endsWith(".jpg")
            }

            var deletedCount = 0
            cacheFiles?.forEach { file ->
                if (file.delete()) {
                    deletedCount++
                }
            }

            statusText.text = "🗑️ 已清除 $deletedCount 个缓存文件"
            imageView.setImageDrawable(null)

        } catch (e: Exception) {
            statusText.text = "❌ 清除缓存失败: ${e.message}"
        }
    }

    private fun testCuteImageGenerator() {
        statusText.text = "🎀 正在从百度/必应下载可爱简笔画..."

        CoroutineScope(Dispatchers.Main).launch {
            try {
                val generator = CuteImageGenerator()

                // 测试不同的关卡，每个关卡对应不同的可爱主题
                val level = (1..20).random() // 随机选择一个关卡
                statusText.text = "🔍 正在搜索第${level}关的可爱图片..."

                val bitmap = generator.generateCuteImage(level, 400, 400, cacheDir)

                if (bitmap != null) {
                    imageView.setImageBitmap(bitmap)
                    statusText.text = "✅ 成功下载可爱简笔画！(第${level}关)"

                    // 显示缓存信息
                    val cacheFile = java.io.File(cacheDir, "cute_image_${level}.jpg")
                    if (cacheFile.exists()) {
                        val fileSize = cacheFile.length() / 1024 // KB
                        statusText.text = statusText.text.toString() + "\n💾 已缓存 ${fileSize}KB"
                    }
                } else {
                    statusText.text = "❌ 下载失败，已使用本地绘制图片"
                }
            } catch (e: Exception) {
                statusText.text = "❌ 错误: ${e.message}"
                e.printStackTrace()
            }
        }
    }
}
