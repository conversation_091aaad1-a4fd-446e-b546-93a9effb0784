#!/usr/bin/env python3
"""
自动下载可爱卡通动物图片的脚本
使用免费的图片API和资源
"""

import requests
import os
import json
from urllib.parse import urlparse

def download_image(url, filename):
    """下载图片到指定文件名"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 确保目录存在
        os.makedirs('app/src/main/res/drawable', exist_ok=True)
        
        # 保存图片
        filepath = f'app/src/main/res/drawable/{filename}'
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ 成功下载: {filename}")
        return True
    except Exception as e:
        print(f"❌ 下载失败 {filename}: {e}")
        return False

def search_pixabay_images(query, api_key=None):
    """搜索Pixabay图片（需要API密钥）"""
    if not api_key:
        print("⚠️  需要Pixabay API密钥，请访问 https://pixabay.com/api/docs/ 获取")
        return []
    
    url = "https://pixabay.com/api/"
    params = {
        'key': api_key,
        'q': query,
        'image_type': 'illustration',
        'category': 'animals',
        'min_width': 512,
        'min_height': 512,
        'safesearch': 'true',
        'per_page': 5
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()
        return data.get('hits', [])
    except Exception as e:
        print(f"搜索失败: {e}")
        return []

def download_sample_images():
    """下载一些示例图片（使用公共域图片）"""
    
    # 这些是一些公共域的可爱动物图片URL示例
    # 实际使用时，你需要从上面提到的网站手动选择和下载
    sample_images = [
        {
            'url': 'https://openclipart.org/image/800px/svg_to_png/321234/1538298636.png',
            'filename': 'puzzle_image_1.png',
            'description': '可爱小猫'
        },
        {
            'url': 'https://openclipart.org/image/800px/svg_to_png/321235/1538298637.png', 
            'filename': 'puzzle_image_2.png',
            'description': '萌萌小狗'
        }
    ]
    
    print("🎨 开始下载示例图片...")
    print("注意：这些只是示例链接，实际使用时请从推荐网站手动选择更可爱的图片！")
    print()
    
    success_count = 0
    for img in sample_images:
        print(f"正在下载 {img['description']}...")
        if download_image(img['url'], img['filename']):
            success_count += 1
    
    print(f"\n✨ 完成！成功下载 {success_count}/{len(sample_images)} 张图片")
    
    if success_count < len(sample_images):
        print("\n💡 建议手动下载：")
        print("1. 访问 https://pixabay.com/")
        print("2. 搜索 'cute cartoon cat'")
        print("3. 选择 Illustrations > PNG > Large")
        print("4. 下载喜欢的图片并重命名为 puzzle_image_*.png")

def show_manual_download_guide():
    """显示手动下载指南"""
    print("🎯 手动下载指南")
    print("=" * 50)
    print()
    
    animals = [
        ("puzzle_image_1.png", "可爱小猫", "cute cartoon cat kawaii"),
        ("puzzle_image_2.png", "萌萌小狗", "cute cartoon dog golden"),
        ("puzzle_image_3.png", "呆萌熊猫", "cute panda cartoon kawaii"),
        ("puzzle_image_4.png", "粉嫩兔子", "cute bunny cartoon kawaii"),
        ("puzzle_image_5.png", "企鹅宝宝", "cute penguin cartoon kawaii")
    ]
    
    for filename, description, search_term in animals:
        print(f"📁 {filename} ({description})")
        print(f"   🔍 搜索关键词: {search_term}")
        print(f"   🌐 推荐网站: https://pixabay.com/")
        print(f"   📐 要求: PNG格式, 至少512x512px, 透明背景")
        print()
    
    print("🔗 推荐下载网站:")
    print("• Pixabay: https://pixabay.com/ (免费，高质量)")
    print("• Freepik: https://www.freepik.com/ (免费，需注册)")
    print("• OpenClipart: https://openclipart.org/ (完全免费)")
    print("• Flaticon: https://www.flaticon.com/ (免费，需注明来源)")
    print()
    
    print("📋 下载步骤:")
    print("1. 访问推荐网站")
    print("2. 搜索对应的关键词")
    print("3. 筛选: PNG格式 + 大尺寸 + 动物分类")
    print("4. 选择最可爱的图片下载")
    print("5. 重命名为对应的文件名")
    print("6. 放入 app/src/main/res/drawable/ 目录")

def main():
    """主函数"""
    print("🎨 可爱卡通动物图片下载工具")
    print("=" * 40)
    print()
    
    print("选择操作:")
    print("1. 显示手动下载指南（推荐）")
    print("2. 尝试下载示例图片（可能失效）")
    print("3. 退出")
    print()
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        show_manual_download_guide()
    elif choice == "2":
        download_sample_images()
    elif choice == "3":
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
