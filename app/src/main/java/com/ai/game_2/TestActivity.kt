package com.ai.game_2

import android.os.Bundle
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class TestActivity : AppCompatActivity() {

    private lateinit var imageView: ImageView
    private lateinit var statusText: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            // 创建一个简单的布局来测试CuteImageGenerator
            createTestLayout()

            Toast.makeText(this, "测试Activity启动成功！", Toast.LENGTH_SHORT).show()

            // 测试CuteImageGenerator
            testCuteImageGenerator()

        } catch (e: Exception) {
            Toast.makeText(this, "错误: ${e.message}", Toast.LENGTH_LONG).show()
            e.printStackTrace()
        }
    }

    private fun createTestLayout() {
        val scrollView = ScrollView(this)
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        // 标题
        val titleText = TextView(this).apply {
            text = "🎀 可爱简笔画下载测试"
            textSize = 20f
            setTypeface(null, android.graphics.Typeface.BOLD)
            gravity = android.view.Gravity.CENTER
        }
        layout.addView(titleText)

        // 状态文本
        statusText = TextView(this).apply {
            text = "准备测试从百度/必应下载可爱简笔画..."
            textSize = 14f
            setPadding(0, 30, 0, 20)
        }
        layout.addView(statusText)

        // 图片显示
        imageView = ImageView(this).apply {
            layoutParams = LinearLayout.LayoutParams(400, 400).apply {
                topMargin = 20
                gravity = android.view.Gravity.CENTER_HORIZONTAL
            }
            scaleType = ImageView.ScaleType.CENTER_CROP
            setBackgroundColor(android.graphics.Color.LTGRAY)
        }
        layout.addView(imageView)

        // 按钮布局
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                topMargin = 30
            }
        }

        val testButton = Button(this).apply {
            text = "🔄 重新下载"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                rightMargin = 10
            }
            setOnClickListener { testCuteImageGenerator() }
        }
        buttonLayout.addView(testButton)

        val clearCacheButton = Button(this).apply {
            text = "🗑️ 清除缓存"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                leftMargin = 5
                rightMargin = 5
            }
            setOnClickListener { clearCache() }
        }
        buttonLayout.addView(clearCacheButton)

        val testNetworkButton = Button(this).apply {
            text = "🌐 测试网络"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                leftMargin = 5
            }
            setOnClickListener { testNetworkConnection() }
        }
        buttonLayout.addView(testNetworkButton)

        layout.addView(buttonLayout)

        // 说明文本
        val infoText = TextView(this).apply {
            text = """
                📝 测试说明：
                • 点击"重新下载"测试从网络下载可爱简笔画
                • 支持百度和必应图片搜索
                • 下载失败时自动使用本地绘制图片
                • 图片会自动缓存到本地
                • 每次测试随机选择不同主题
            """.trimIndent()
            textSize = 12f
            setPadding(0, 30, 0, 0)
            setTextColor(android.graphics.Color.GRAY)
        }
        layout.addView(infoText)

        scrollView.addView(layout)
        setContentView(scrollView)
    }

    private fun clearCache() {
        try {
            val cacheFiles = cacheDir.listFiles { file ->
                file.name.startsWith("cute_image_") && file.name.endsWith(".jpg")
            }

            var deletedCount = 0
            cacheFiles?.forEach { file ->
                if (file.delete()) {
                    deletedCount++
                }
            }

            statusText.text = "🗑️ 已清除 $deletedCount 个缓存文件"
            imageView.setImageDrawable(null)

        } catch (e: Exception) {
            statusText.text = "❌ 清除缓存失败: ${e.message}"
        }
    }

    private fun testNetworkConnection() {
        statusText.text = "🌐 正在测试网络连接..."

        CoroutineScope(Dispatchers.Main).launch {
            try {
                withContext(Dispatchers.IO) {
                    val testUrls = listOf(
                        "https://image.baidu.com/" to "百度图片",
                        "https://cn.bing.com/" to "必应中国",
                        "https://www.google.com/" to "Google"
                    )

                    val results = mutableListOf<String>()

                    for ((url, name) in testUrls) {
                        try {
                            val connection = java.net.URL(url).openConnection() as java.net.HttpURLConnection
                            connection.connectTimeout = 5000
                            connection.readTimeout = 5000
                            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

                            val responseCode = connection.responseCode
                            connection.disconnect()

                            if (responseCode == 200) {
                                results.add("✅ $name: 连接成功")
                            } else {
                                results.add("⚠️ $name: HTTP $responseCode")
                            }
                        } catch (e: Exception) {
                            results.add("❌ $name: ${e.message}")
                        }
                    }

                    withContext(Dispatchers.Main) {
                        statusText.text = "🌐 网络连接测试结果:\n" + results.joinToString("\n")
                    }
                }
            } catch (e: Exception) {
                statusText.text = "❌ 网络测试失败: ${e.message}"
            }
        }
    }

    private fun testCuteImageGenerator() {
        statusText.text = "🎀 开始测试可爱简笔画下载功能..."
        imageView.setImageDrawable(null)

        CoroutineScope(Dispatchers.Main).launch {
            try {
                val generator = CuteImageGenerator()

                // 测试不同的关卡，每个关卡对应不同的可爱主题
                val level = (1..20).random() // 随机选择一个关卡
                val themes = listOf(
                    "可爱小猫简笔画", "可爱小狗简笔画", "可爱小兔子简笔画", "可爱小熊简笔画",
                    "可爱小鸟简笔画", "可爱小鱼简笔画", "可爱蝴蝶简笔画", "可爱花朵简笔画",
                    "可爱太阳简笔画", "可爱月亮简笔画", "可爱星星简笔画", "可爱彩虹简笔画",
                    "可爱房子简笔画", "可爱汽车简笔画", "可爱飞机简笔画", "可爱船简笔画",
                    "可爱蛋糕简笔画", "可爱冰淇淋简笔画", "可爱水果简笔画", "可爱蔬菜简笔画"
                )
                val theme = themes[(level - 1) % themes.size]

                statusText.text = "🔍 第${level}关: $theme\n📡 正在搜索真实可爱图片...\n🌐 百度 → 必应 → Pixabay → Unsplash → Pexels"

                val startTime = System.currentTimeMillis()
                val bitmap = generator.generateCuteImage(level, 400, 400, cacheDir)
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime

                if (bitmap != null) {
                    imageView.setImageBitmap(bitmap)

                    // 检查是否是从缓存加载的
                    val cacheFile = java.io.File(cacheDir, "cute_image_${level}.jpg")
                    val isFromCache = cacheFile.exists() && duration < 1000

                    var statusMessage = if (isFromCache) {
                        "✅ 从缓存加载真实图片！(${duration}ms)"
                    } else {
                        "✅ 下载真实可爱图片成功！(${duration}ms)"
                    }

                    statusMessage += "\n🎨 主题: $theme"
                    statusMessage += "\n📐 尺寸: ${bitmap.width}x${bitmap.height}"
                    statusMessage += "\n🌟 图片源: 真实无版权图片"

                    // 显示缓存信息
                    if (cacheFile.exists()) {
                        val fileSize = cacheFile.length() / 1024 // KB
                        statusMessage += "\n💾 缓存: ${fileSize}KB"
                    }

                    statusText.text = statusMessage
                } else {
                    statusText.text = "❌ 所有真实图片源都失败了 (${duration}ms)\n⚠️ 这不应该发生，请检查网络连接\n📝 主题: $theme"
                }
            } catch (e: Exception) {
                statusText.text = "❌ 测试失败: ${e.message}\n📱 请检查网络连接"
                e.printStackTrace()
            }
        }
    }
}
