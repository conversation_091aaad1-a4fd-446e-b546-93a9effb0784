package com.ai.game_2

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import kotlinx.coroutines.*

class MainActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 隐藏状态栏和导航栏，实现全屏
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )

        setContentView(R.layout.activity_main)

        setupUI()
    }
    
    private fun setupUI() {
        val startGameButton = findViewById<Button>(R.id.btn_start_game)
        val levelSelectButton = findViewById<Button>(R.id.btn_level_select)
        val testDownloadButton = findViewById<Button>(R.id.btn_test_download)
        val exitButton = findViewById<Button>(R.id.btn_exit)

        startGameButton.setOnClickListener {
            // 开始可爱主题第一关
            startGame(GameTheme.CUTE, 1)
        }

        levelSelectButton.setOnClickListener {
            val intent = Intent(this, ThemeSelectActivity::class.java)
            startActivity(intent)
        }

        testDownloadButton.setOnClickListener {
            testCuteImageDownload()
        }

        exitButton.setOnClickListener {
            finish()
        }
    }
    
    private fun startGame(theme: GameTheme, level: Int) {
        val intent = Intent(this, PuzzleGameActivity::class.java)
        intent.putExtra("theme", theme.name)
        intent.putExtra("level", level)
        startActivity(intent)
    }

    private fun testCuteImageDownload() {
        Toast.makeText(this, "开始测试下载...", Toast.LENGTH_SHORT).show()

        CoroutineScope(Dispatchers.Main).launch {
            try {
                val generator = CuteImageGenerator(this@MainActivity)

                // 清理之前的数据
                generator.clearAllData()
                Log.d("MainActivity", "已清理之前的数据")

                // 测试搜索功能
                val searchResult = generator.testSearch(1)
                Log.d("MainActivity", "搜索测试结果:\n$searchResult")

                // 测试实际下载
                Log.d("MainActivity", "开始实际下载测试...")
                val bitmap = generator.generateCuteImage(1)

                if (bitmap != null) {
                    Log.d("MainActivity", "✅ 第1关下载成功！图片尺寸: ${bitmap.width}x${bitmap.height}")
                    Toast.makeText(this@MainActivity, "下载成功！图片尺寸: ${bitmap.width}x${bitmap.height}", Toast.LENGTH_LONG).show()

                    // 检查本地文件
                    val downloadedLevels = generator.getDownloadedLevels()
                    Log.d("MainActivity", "已下载关卡: $downloadedLevels")
                } else {
                    Log.e("MainActivity", "❌ 第1关下载失败")
                    Toast.makeText(this@MainActivity, "下载失败，请查看Logcat", Toast.LENGTH_LONG).show()

                    // 检查失败记录
                    val failedLevels = generator.getFailedLevels()
                    Log.d("MainActivity", "失败关卡: $failedLevels")
                }

            } catch (e: Exception) {
                Log.e("MainActivity", "测试异常: ${e.message}", e)
                Toast.makeText(this@MainActivity, "测试异常: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }
}
