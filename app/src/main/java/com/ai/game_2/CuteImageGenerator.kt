package com.ai.game_2

import android.content.Context
import android.graphics.*
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.cos
import kotlin.math.sin

class CuteImageGenerator(private val context: Context) {

    private val width = 400
    private val height = 400

    // 图片永久存储目录（使用内部存储，不会被清理）
    private val imageDir = File(context.filesDir, "cute_images")

    // 下载失败记录文件
    private val failedDownloadsFile = File(context.filesDir, "failed_downloads.txt")

    // 简笔画搜索关键词
    private val cuteThemes = listOf(
        "cat simple drawing outline",
        "car simple drawing outline",
        "dinosaur simple drawing outline",
        "castle simple drawing outline",
        "rocket simple drawing outline",
        "flower simple drawing outline",
        "robot simple drawing outline",
        "butterfly simple drawing outline",
        "house simple drawing outline",
        "unicorn simple drawing outline"
    )

    init {
        // 创建图片存储目录
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        }
    }

    // 免费图片网站API（无版权问题）
    private val freeImageSources = listOf(
        "https://pixabay.com/api/",
        "https://api.unsplash.com/search/photos",
        "https://api.pexels.com/v1/search"
    )

    // 百度/Bing搜索关键词（用于搜索真正的可爱简笔画）
    private val searchKeywords = mapOf(
        1 to listOf("可爱小猫简笔画", "cute cat drawing", "小猫卡通简笔画"),
        2 to listOf("可爱小汽车简笔画", "cute car drawing", "卡通汽车简笔画"),
        3 to listOf("可爱恐龙简笔画", "cute dinosaur drawing", "卡通恐龙简笔画"),
        4 to listOf("可爱城堡简笔画", "cute castle drawing", "童话城堡简笔画"),
        5 to listOf("可爱火箭简笔画", "cute rocket drawing", "卡通火箭简笔画"),
        6 to listOf("可爱花朵简笔画", "cute flower drawing", "卡通花朵简笔画"),
        7 to listOf("可爱机器人简笔画", "cute robot drawing", "卡通机器人简笔画"),
        8 to listOf("可爱蝴蝶简笔画", "cute butterfly drawing", "卡通蝴蝶简笔画"),
        9 to listOf("可爱房子简笔画", "cute house drawing", "卡通房子简笔画"),
        10 to listOf("可爱独角兽简笔画", "cute unicorn drawing", "卡通独角兽简笔画")
    )
    
    // 检查关卡是否可用（下载是否成功）
    fun isLevelAvailable(level: Int): Boolean {
        // 检查是否有本地图片
        if (hasLocalImage(level)) {
            return true
        }

        // 检查是否在失败列表中
        return !isLevelFailed(level)
    }

    // 异步生成可爱图片
    suspend fun generateCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 检查关卡是否可用
                if (!isLevelAvailable(level)) {
                    return@withContext null // 关卡不可用，返回null
                }

                // 尝试从本地或网络获取图片
                downloadCuteImage(level)
            } catch (e: Exception) {
                // 下载失败，标记关卡为不可用
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 同步版本（保持兼容性）
    fun generateCuteImageSync(level: Int): Bitmap? {
        return runBlocking {
            generateCuteImage(level)
        }
    }
    
    // 从本地或网络获取简笔画图片
    private suspend fun downloadCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 首先检查本地是否已有图片
                val localBitmap = getLocalImage(level)
                if (localBitmap != null) {
                    return@withContext localBitmap // 直接返回本地图片，不重复下载
                }

                // 本地没有，从百度/Bing搜索下载简笔画
                val keywords = searchKeywords[level]
                if (keywords != null) {
                    for (keyword in keywords) {
                        try {
                            val imageUrls = searchImagesFromBaidu(keyword)
                            for (imageUrl in imageUrls.take(3)) { // 每个关键词最多尝试3张图片
                                try {
                                    val bitmap = downloadAndSaveImage(imageUrl, level)
                                    if (bitmap != null) {
                                        return@withContext resizeBitmap(bitmap, width, height)
                                    }
                                } catch (e: Exception) {
                                    continue
                                }
                            }
                        } catch (e: Exception) {
                            continue
                        }
                    }
                }



                // 所有下载都失败，标记为失败
                markLevelAsFailed(level)
                null
            } catch (e: Exception) {
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 检查本地是否有图片
    private fun hasLocalImage(level: Int): Boolean {
        val imageFile = File(imageDir, "cute_level_$level.png")
        return imageFile.exists()
    }

    // 获取本地图片
    private fun getLocalImage(level: Int): Bitmap? {
        return try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            if (imageFile.exists()) {
                BitmapFactory.decodeFile(imageFile.absolutePath)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    // 下载图片并永久保存
    private suspend fun downloadAndSaveImage(imageUrl: String, level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val bitmap = downloadImageFromUrl(imageUrl)
                if (bitmap != null) {
                    // 永久保存到本地
                    saveBitmapToLocal(bitmap, level)
                    bitmap
                } else {
                    null
                }
            } catch (e: Exception) {
                null
            }
        }
    }

    // 永久保存图片到本地
    private fun saveBitmapToLocal(bitmap: Bitmap, level: Int) {
        try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            val outputStream = FileOutputStream(imageFile)
            bitmap.compress(Bitmap.CompressFormat.PNG, 95, outputStream) // 高质量保存
            outputStream.close()
        } catch (e: Exception) {
            // 保存失败不影响主要功能
        }
    }

    // 检查关卡是否下载失败
    private fun isLevelFailed(level: Int): Boolean {
        return try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",")
                failedLevels.contains(level.toString())
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }

    // 标记关卡为下载失败
    private fun markLevelAsFailed(level: Int) {
        try {
            val failedLevels = if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText().split(",").toMutableSet()
            } else {
                mutableSetOf()
            }

            failedLevels.add(level.toString())
            failedDownloadsFile.writeText(failedLevels.joinToString(","))
        } catch (e: Exception) {
            // 记录失败不影响主要功能
        }
    }

    // 清理所有本地图片和失败记录（重置功能）
    fun clearAllData() {
        try {
            // 删除所有本地图片
            if (imageDir.exists()) {
                imageDir.listFiles()?.forEach { file ->
                    if (file.name.startsWith("cute_level_")) {
                        file.delete()
                    }
                }
            }

            // 删除失败记录
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.delete()
            }
        } catch (e: Exception) {
            // 清理失败不影响主要功能
        }
    }

    // 重新尝试失败的关卡
    fun retryFailedLevel(level: Int) {
        try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",").toMutableSet()
                failedLevels.remove(level.toString())

                if (failedLevels.isEmpty()) {
                    failedDownloadsFile.delete()
                } else {
                    failedDownloadsFile.writeText(failedLevels.joinToString(","))
                }
            }
        } catch (e: Exception) {
            // 重试失败不影响主要功能
        }
    }

    // 检查本地存储大小
    fun getLocalStorageSize(): Long {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()?.sumOf { it.length() } ?: 0L
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }

    // 获取已下载的关卡列表
    fun getDownloadedLevels(): List<Int> {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()
                    ?.filter { it.name.startsWith("cute_level_") && it.name.endsWith(".png") }
                    ?.mapNotNull { file ->
                        val levelStr = file.name.removePrefix("cute_level_").removeSuffix(".png")
                        levelStr.toIntOrNull()
                    }
                    ?.sorted() ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    // 获取失败的关卡列表
    fun getFailedLevels(): List<Int> {
        return try {
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText()
                    .split(",")
                    .mapNotNull { it.toIntOrNull() }
                    .sorted()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    


    // 从百度搜索简笔画图片
    private suspend fun searchImagesFromBaidu(keyword: String): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                val encodedKeyword = java.net.URLEncoder.encode(keyword, "UTF-8")
                val searchUrl = "https://image.baidu.com/search/index?tn=baiduimage&word=$encodedKeyword&ie=utf-8"

                // 获取搜索结果页面
                val connection = java.net.URL(searchUrl).openConnection() as java.net.HttpURLConnection
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                connection.connectTimeout = 10000
                connection.readTimeout = 10000

                val html = connection.inputStream.bufferedReader().use { it.readText() }
                connection.disconnect()

                // 解析HTML，提取图片URL
                extractImageUrlsFromBaiduHtml(html)
            } catch (e: Exception) {
                // 百度搜索失败，尝试Bing
                searchImagesFromBing(keyword)
            }
        }
    }

    // 从Bing搜索简笔画图片（备用方案）
    private suspend fun searchImagesFromBing(keyword: String): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                val encodedKeyword = java.net.URLEncoder.encode(keyword, "UTF-8")
                val searchUrl = "https://www.bing.com/images/search?q=$encodedKeyword&form=HDRSC2"

                val connection = java.net.URL(searchUrl).openConnection() as java.net.HttpURLConnection
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                connection.connectTimeout = 10000
                connection.readTimeout = 10000

                val html = connection.inputStream.bufferedReader().use { it.readText() }
                connection.disconnect()

                // 解析HTML，提取图片URL
                extractImageUrlsFromBingHtml(html)
            } catch (e: Exception) {
                emptyList()
            }
        }
    }

    // 从百度HTML中提取图片URL
    private fun extractImageUrlsFromBaiduHtml(html: String): List<String> {
        val imageUrls = mutableListOf<String>()
        try {
            // 百度图片搜索结果中的图片URL通常在objURL字段中
            val pattern = """"objURL":"([^"]+)"""".toRegex()
            val matches = pattern.findAll(html)

            for (match in matches.take(10)) { // 最多提取10个URL
                val url = match.groupValues[1]
                    .replace("\\u002F", "/")
                    .replace("\\", "")

                // 过滤出图片格式
                if (url.contains(".jpg", true) ||
                    url.contains(".jpeg", true) ||
                    url.contains(".png", true) ||
                    url.contains(".gif", true)) {
                    imageUrls.add(url)
                }
            }
        } catch (e: Exception) {
            // 解析失败
        }
        return imageUrls
    }

    // 从Bing HTML中提取图片URL
    private fun extractImageUrlsFromBingHtml(html: String): List<String> {
        val imageUrls = mutableListOf<String>()
        try {
            // Bing图片搜索结果中的图片URL
            val pattern = """"murl":"([^"]+)"""".toRegex()
            val matches = pattern.findAll(html)

            for (match in matches.take(10)) { // 最多提取10个URL
                val url = match.groupValues[1]
                    .replace("\\u002F", "/")
                    .replace("\\", "")

                // 过滤出图片格式
                if (url.contains(".jpg", true) ||
                    url.contains(".jpeg", true) ||
                    url.contains(".png", true) ||
                    url.contains(".gif", true)) {
                    imageUrls.add(url)
                }
            }
        } catch (e: Exception) {
            // 解析失败
        }
        return imageUrls
    }

    // 从URL下载图片
    private suspend fun downloadImageFromUrl(imageUrl: String): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val url = URL(imageUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.connectTimeout = 5000
                connection.readTimeout = 10000
                connection.doInput = true
                connection.connect()
                
                val inputStream: InputStream = connection.inputStream
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream.close()
                connection.disconnect()
                
                bitmap
            } catch (e: Exception) {
                null
            }
        }
    }
    
    // 调整图片大小
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        return Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
    }
}
