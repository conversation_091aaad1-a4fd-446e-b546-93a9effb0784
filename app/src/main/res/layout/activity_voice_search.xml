<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/header_background"
        android:gravity="center_vertical">

        <Button
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/ic_back"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="语音搜图 🎤"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text_color"
            android:gravity="center" />

        <!-- 占位符，保持标题居中 -->
        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />

    </LinearLayout>

    <!-- 说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="说出你想要的图片内容，我们将为你搜索并制作拼图！🔍✨"
        android:textSize="16sp"
        android:textColor="@color/secondary_text_color"
        android:gravity="center"
        android:padding="16dp"
        android:background="@color/footer_background" />

    <!-- 输入区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 搜索输入框 -->
        <EditText
            android:id="@+id/et_search_input"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:hint="请输入或说出搜索内容..."
            android:textSize="16sp"
            android:padding="16dp"
            android:background="@drawable/button_secondary"
            android:layout_marginBottom="16dp" />

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_voice_input"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="语音输入 🎤"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/button_primary"
                android:textColor="@android:color/white"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_search"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="搜索图片 🔍"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/button_primary"
                android:textColor="@android:color/white"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 状态显示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="请输入搜索内容或点击语音输入"
            android:textSize="14sp"
            android:textColor="@color/secondary_text_color"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            style="?android:attr/progressBarStyleHorizontal"
            android:indeterminate="true" />

    </LinearLayout>

    <!-- 图片预览区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="下载的图片预览："
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text_color"
            android:layout_marginBottom="8dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_images"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

    <!-- 开始游戏按钮 -->
    <Button
        android:id="@+id/btn_start_game"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="开始拼图游戏 🧩"
        android:textSize="18sp"
        android:textStyle="bold"
        android:background="@drawable/button_primary"
        android:textColor="@android:color/white"
        android:layout_margin="16dp"
        android:enabled="false" />

</LinearLayout>
