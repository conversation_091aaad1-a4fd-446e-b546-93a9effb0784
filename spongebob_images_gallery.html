<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧽 海绵宝宝图片库 - 拼图游戏专用</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #FF6B35;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .header p {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 20px;
        }
        
        .character-section {
            background: linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 3px solid #FF6B35;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .character-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
        }
        
        .character-title span {
            font-size: 2em;
            margin-right: 15px;
        }
        
        .search-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .search-link {
            display: block;
            background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
            color: white;
            text-decoration: none;
            padding: 20px;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .search-link:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        
        .website-name {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .search-term {
            font-size: 0.9em;
            opacity: 0.9;
            margin-bottom: 10px;
        }
        
        .download-btn {
            background: #FF6B35;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            position: absolute;
            top: 15px;
            right: 15px;
        }
        
        .tips {
            background: linear-gradient(135deg, #98FB98 0%, #90EE90 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #32CD32;
        }
        
        .tips h3 {
            color: #2E8B57;
            margin-top: 0;
            font-size: 1.3em;
        }
        
        .requirements {
            background: linear-gradient(135deg, #FFE4B5 0%, #FFEFD5 100%);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            border-left: 5px solid #FFA500;
        }
        
        .requirements h3 {
            color: #FF8C00;
            margin-top: 0;
            font-size: 1.3em;
        }
        
        .requirements ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .requirements li {
            margin-bottom: 8px;
            padding-left: 25px;
            position: relative;
        }
        
        .requirements li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧽 海绵宝宝拼图图片库</h1>
            <p>为你的3x3拼图游戏下载10个海绵宝宝角色图片！</p>
        </div>
        
        <div class="tips">
            <h3>💡 使用说明</h3>
            <p>点击下面的链接直接跳转到对应网站的搜索结果页面，然后选择你喜欢的海绵宝宝角色图片下载！</p>
            <p><strong>⚠️ 重要：游戏已更新为10个关卡，全部3x3格式，海绵宝宝主题！</strong></p>
            <p><strong>下载后请重命名为对应的文件名，并放入项目的 app/src/main/res/drawable/ 目录。</strong></p>
        </div>

        <!-- 第1关：海绵宝宝 -->
        <div class="character-section">
            <div class="character-title">
                <span>🧽</span>
                <div>
                    <div>第1关：海绵宝宝 SpongeBob</div>
                    <div style="font-size: 0.8em; color: #666;">文件名: puzzle_image_1.png</div>
                </div>
            </div>
            <div class="search-links">
                <a href="https://pixabay.com/illustrations/search/spongebob%20squarepants/" class="search-link" target="_blank">
                    <div class="website-name">📸 Pixabay</div>
                    <div class="search-term">搜索: spongebob squarepants</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.freepik.com/search?format=search&query=spongebob%20cartoon" class="search-link" target="_blank">
                    <div class="website-name">🎨 Freepik</div>
                    <div class="search-term">搜索: spongebob cartoon</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://openclipart.org/search/?query=spongebob" class="search-link" target="_blank">
                    <div class="website-name">🆓 OpenClipart</div>
                    <div class="search-term">搜索: spongebob</div>
                    <span class="download-btn">完全免费</span>
                </a>
            </div>
        </div>

        <!-- 第2关：派大星 -->
        <div class="character-section">
            <div class="character-title">
                <span>⭐</span>
                <div>
                    <div>第2关：派大星 Patrick Star</div>
                    <div style="font-size: 0.8em; color: #666;">文件名: puzzle_image_2.png</div>
                </div>
            </div>
            <div class="search-links">
                <a href="https://pixabay.com/illustrations/search/patrick%20star%20spongebob/" class="search-link" target="_blank">
                    <div class="website-name">📸 Pixabay</div>
                    <div class="search-term">搜索: patrick star spongebob</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.freepik.com/search?format=search&query=patrick%20starfish" class="search-link" target="_blank">
                    <div class="website-name">🎨 Freepik</div>
                    <div class="search-term">搜索: patrick starfish</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://openclipart.org/search/?query=patrick%20star" class="search-link" target="_blank">
                    <div class="website-name">🆓 OpenClipart</div>
                    <div class="search-term">搜索: patrick star</div>
                    <span class="download-btn">完全免费</span>
                </a>
            </div>
        </div>

        <!-- 第3关：章鱼哥 -->
        <div class="character-section">
            <div class="character-title">
                <span>🐙</span>
                <div>
                    <div>第3关：章鱼哥 Squidward</div>
                    <div style="font-size: 0.8em; color: #666;">文件名: puzzle_image_3.png</div>
                </div>
            </div>
            <div class="search-links">
                <a href="https://pixabay.com/illustrations/search/squidward%20tentacles/" class="search-link" target="_blank">
                    <div class="website-name">📸 Pixabay</div>
                    <div class="search-term">搜索: squidward tentacles</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.freepik.com/search?format=search&query=squidward%20spongebob" class="search-link" target="_blank">
                    <div class="website-name">🎨 Freepik</div>
                    <div class="search-term">搜索: squidward spongebob</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://openclipart.org/search/?query=squidward" class="search-link" target="_blank">
                    <div class="website-name">🆓 OpenClipart</div>
                    <div class="search-term">搜索: squidward</div>
                    <span class="download-btn">完全免费</span>
                </a>
            </div>
        </div>

        <!-- 第4关：蟹老板 -->
        <div class="character-section">
            <div class="character-title">
                <span>🦀</span>
                <div>
                    <div>第4关：蟹老板 Mr. Krabs</div>
                    <div style="font-size: 0.8em; color: #666;">文件名: puzzle_image_4.png</div>
                </div>
            </div>
            <div class="search-links">
                <a href="https://pixabay.com/illustrations/search/mr%20krabs%20spongebob/" class="search-link" target="_blank">
                    <div class="website-name">📸 Pixabay</div>
                    <div class="search-term">搜索: mr krabs spongebob</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.freepik.com/search?format=search&query=eugene%20krabs" class="search-link" target="_blank">
                    <div class="website-name">🎨 Freepik</div>
                    <div class="search-term">搜索: eugene krabs</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://openclipart.org/search/?query=mr%20krabs" class="search-link" target="_blank">
                    <div class="website-name">🆓 OpenClipart</div>
                    <div class="search-term">搜索: mr krabs</div>
                    <span class="download-btn">完全免费</span>
                </a>
            </div>
        </div>

        <!-- 第5关：珊迪 -->
        <div class="character-section">
            <div class="character-title">
                <span>🐿️</span>
                <div>
                    <div>第5关：珊迪 Sandy Cheeks</div>
                    <div style="font-size: 0.8em; color: #666;">文件名: puzzle_image_5.png</div>
                </div>
            </div>
            <div class="search-links">
                <a href="https://pixabay.com/illustrations/search/sandy%20cheeks%20spongebob/" class="search-link" target="_blank">
                    <div class="website-name">📸 Pixabay</div>
                    <div class="search-term">搜索: sandy cheeks spongebob</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.freepik.com/search?format=search&query=sandy%20squirrel" class="search-link" target="_blank">
                    <div class="website-name">🎨 Freepik</div>
                    <div class="search-term">搜索: sandy squirrel</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://openclipart.org/search/?query=sandy%20cheeks" class="search-link" target="_blank">
                    <div class="website-name">🆓 OpenClipart</div>
                    <div class="search-term">搜索: sandy cheeks</div>
                    <span class="download-btn">完全免费</span>
                </a>
            </div>
        </div>

        <div class="requirements">
            <h3>📐 图片要求</h3>
            <ul>
                <li><strong>格式：</strong>PNG 或 JPG（真实网络图片）</li>
                <li><strong>尺寸：</strong>至少 512×512px，推荐 1024×1024px</li>
                <li><strong>风格：</strong>卡通、动画风格，海绵宝宝角色</li>
                <li><strong>内容：</strong>单个角色，背景简洁，适合3x3拼图</li>
                <li><strong>质量：</strong>高清无模糊，避免像素化或马赛克效果</li>
                <li><strong>版权：</strong>选择免费使用或粉丝艺术作品</li>
            </ul>
        </div>
    </div>
</body>
</html>
