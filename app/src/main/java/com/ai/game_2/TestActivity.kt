package com.ai.game_2

import android.os.Bundle
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class TestActivity : AppCompatActivity() {

    private lateinit var imageView: ImageView
    private lateinit var statusText: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            // 创建一个简单的布局来测试CuteImageGenerator
            createTestLayout()

            Toast.makeText(this, "测试Activity启动成功！", Toast.LENGTH_SHORT).show()

            // 测试CuteImageGenerator
            testCuteImageGenerator()

        } catch (e: Exception) {
            Toast.makeText(this, "错误: ${e.message}", Toast.LENGTH_LONG).show()
            e.printStackTrace()
        }
    }

    private fun createTestLayout() {
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        statusText = TextView(this).apply {
            text = "正在测试CuteImageGenerator..."
            textSize = 16f
        }
        layout.addView(statusText)

        imageView = ImageView(this).apply {
            layoutParams = LinearLayout.LayoutParams(400, 400).apply {
                topMargin = 50
            }
            scaleType = ImageView.ScaleType.CENTER_CROP
        }
        layout.addView(imageView)

        val testButton = Button(this).apply {
            text = "重新测试"
            setOnClickListener { testCuteImageGenerator() }
        }
        layout.addView(testButton)

        setContentView(layout)
    }

    private fun testCuteImageGenerator() {
        statusText.text = "正在生成可爱图片..."

        CoroutineScope(Dispatchers.Main).launch {
            try {
                val generator = CuteImageGenerator()
                val bitmap = generator.generateCuteImage(1, 400, 400, cacheDir)

                if (bitmap != null) {
                    imageView.setImageBitmap(bitmap)
                    statusText.text = "成功生成可爱图片！"
                } else {
                    statusText.text = "生成图片失败"
                }
            } catch (e: Exception) {
                statusText.text = "错误: ${e.message}"
                e.printStackTrace()
            }
        }
    }
}
