<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧽 海绵宝宝图片库 - 拼图游戏专用</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #4a90e2;
            font-size: 2.5em;
            margin-bottom: 30px;
        }
        .animal-section {
            margin-bottom: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #4a90e2;
        }
        .animal-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .animal-title span {
            font-size: 2em;
            margin-right: 10px;
        }
        .search-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .search-link {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            display: block;
        }
        .search-link:hover {
            border-color: #4a90e2;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 144, 226, 0.2);
        }
        .website-name {
            font-weight: bold;
            color: #4a90e2;
            font-size: 1.1em;
        }
        .search-term {
            color: #666;
            font-style: italic;
            margin-top: 5px;
        }
        .download-btn {
            background: #28a745;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9em;
            margin-top: 8px;
            display: inline-block;
            transition: background 0.3s ease;
        }
        .download-btn:hover {
            background: #218838;
            color: white;
        }
        .tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        .tips h3 {
            color: #856404;
            margin-top: 0;
        }
        .requirements {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .requirements h3 {
            color: #0c5460;
            margin-top: 0;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👸 小女孩最爱的公主角色图片库</h1>
        
        <div class="tips">
            <h3>💡 使用说明</h3>
            <p>点击下面的链接直接跳转到对应网站的搜索结果页面，然后选择你喜欢的公主角色图片下载！</p>
            <p><strong>⚠️ 重要：我已经删除了所有XML矢量图，现在需要下载真实的JPG/PNG图片！</strong></p>
            <p><strong>下载后请重命名为对应的文件名，并放入项目的 app/src/main/res/drawable/ 目录。</strong></p>
        </div>

        <div class="requirements">
            <h3>📐 图片要求</h3>
            <ul>
                <li><strong>格式：</strong>JPG 或 PNG（真实网络图片，不是XML）</li>
                <li><strong>尺寸：</strong>至少 512×512px，推荐 1024×1024px</li>
                <li><strong>风格：</strong>卡通、可爱、大眼睛、儿童友好</li>
                <li><strong>内容：</strong>单个公主角色，表情甜美，适合儿童</li>
                <li><strong>质量：</strong>高清无模糊，避免像素化或马赛克效果</li>
            </ul>
        </div>

        <div class="animal-section">
            <div class="animal-title">
                <span>👸</span>
                <div>
                    <div>第1关：艾莎公主</div>
                    <div style="font-size: 0.8em; color: #666;">文件名: puzzle_image_1.png</div>
                </div>
            </div>
            <div class="search-links">
                <a href="https://pixabay.com/illustrations/search/elsa%20princess%20frozen/" class="search-link" target="_blank">
                    <div class="website-name">📸 Pixabay</div>
                    <div class="search-term">搜索: elsa princess frozen</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.freepik.com/search?format=search&query=elsa%20frozen%20princess" class="search-link" target="_blank">
                    <div class="website-name">🎨 Freepik</div>
                    <div class="search-term">搜索: elsa frozen princess</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://openclipart.org/search/?query=princess%20elsa" class="search-link" target="_blank">
                    <div class="website-name">🆓 OpenClipart</div>
                    <div class="search-term">搜索: princess elsa</div>
                    <span class="download-btn">完全免费</span>
                </a>
            </div>
        </div>

        <div class="animal-section">
            <div class="animal-title">
                <span>🧜‍♀️</span>
                <div>
                    <div>第2关：美人鱼公主</div>
                    <div style="font-size: 0.8em; color: #666;">文件名: puzzle_image_2.png</div>
                </div>
            </div>
            <div class="search-links">
                <a href="https://pixabay.com/illustrations/search/mermaid%20princess%20ariel/" class="search-link" target="_blank">
                    <div class="website-name">📸 Pixabay</div>
                    <div class="search-term">搜索: mermaid princess ariel</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.freepik.com/search?format=search&query=cute%20mermaid%20princess" class="search-link" target="_blank">
                    <div class="website-name">🎨 Freepik</div>
                    <div class="search-term">搜索: cute mermaid princess</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.flaticon.com/search?word=mermaid%20cute" class="search-link" target="_blank">
                    <div class="website-name">🔷 Flaticon</div>
                    <div class="search-term">搜索: mermaid cute</div>
                    <span class="download-btn">免费下载</span>
                </a>
            </div>
        </div>

        <div class="animal-section">
            <div class="animal-title">
                <span>🦄</span>
                <div>
                    <div>第3关：梦幻独角兽</div>
                    <div style="font-size: 0.8em; color: #666;">文件名: puzzle_image_3.png</div>
                </div>
            </div>
            <div class="search-links">
                <a href="https://pixabay.com/illustrations/search/cute%20unicorn%20kawaii/" class="search-link" target="_blank">
                    <div class="website-name">📸 Pixabay</div>
                    <div class="search-term">搜索: cute unicorn kawaii</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.freepik.com/search?format=search&query=cute%20unicorn%20cartoon" class="search-link" target="_blank">
                    <div class="website-name">🎨 Freepik</div>
                    <div class="search-term">搜索: cute unicorn cartoon</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://openclipart.org/search/?query=unicorn%20cute" class="search-link" target="_blank">
                    <div class="website-name">🆓 OpenClipart</div>
                    <div class="search-term">搜索: unicorn cute</div>
                    <span class="download-btn">完全免费</span>
                </a>
            </div>
        </div>

        <div class="animal-section">
            <div class="animal-title">
                <span>🧚‍♀️</span>
                <div>
                    <div>第4关：花仙子</div>
                    <div style="font-size: 0.8em; color: #666;">文件名: puzzle_image_4.png</div>
                </div>
            </div>
            <div class="search-links">
                <a href="https://pixabay.com/illustrations/search/cute%20fairy%20princess/" class="search-link" target="_blank">
                    <div class="website-name">📸 Pixabay</div>
                    <div class="search-term">搜索: cute fairy princess</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.freepik.com/search?format=search&query=cute%20fairy%20cartoon" class="search-link" target="_blank">
                    <div class="website-name">🎨 Freepik</div>
                    <div class="search-term">搜索: cute fairy cartoon</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.flaticon.com/search?word=fairy%20cute" class="search-link" target="_blank">
                    <div class="website-name">🔷 Flaticon</div>
                    <div class="search-term">搜索: fairy cute</div>
                    <span class="download-btn">免费下载</span>
                </a>
            </div>
        </div>

        <div class="animal-section">
            <div class="animal-title">
                <span>🌈</span>
                <div>
                    <div>第5关：彩虹小马</div>
                    <div style="font-size: 0.8em; color: #666;">文件名: puzzle_image_5.png</div>
                </div>
            </div>
            <div class="search-links">
                <a href="https://pixabay.com/illustrations/search/rainbow%20pony%20cute/" class="search-link" target="_blank">
                    <div class="website-name">📸 Pixabay</div>
                    <div class="search-term">搜索: rainbow pony cute</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://www.freepik.com/search?format=search&query=cute%20rainbow%20pony" class="search-link" target="_blank">
                    <div class="website-name">🎨 Freepik</div>
                    <div class="search-term">搜索: cute rainbow pony</div>
                    <span class="download-btn">免费下载</span>
                </a>
                <a href="https://openclipart.org/search/?query=rainbow%20pony" class="search-link" target="_blank">
                    <div class="website-name">🆓 OpenClipart</div>
                    <div class="search-term">搜索: rainbow pony</div>
                    <span class="download-btn">完全免费</span>
                </a>
            </div>
        </div>

        <div class="tips">
            <h3>🔧 下载后的处理步骤</h3>
            <ol>
                <li>选择你最喜欢的图片点击下载</li>
                <li>如果是Freepik，可能需要免费注册账户</li>
                <li>下载完成后，重命名为对应的文件名（如 puzzle_image_1.png）</li>
                <li>删除项目中原有的 puzzle_image_*.xml 文件</li>
                <li>将新的PNG文件放入 app/src/main/res/drawable/ 目录</li>
                <li>重新构建项目：<code>./gradlew build</code></li>
            </ol>
        </div>

        <div class="requirements">
            <h3>⚖️ 版权说明</h3>
            <ul>
                <li><strong>Pixabay：</strong>免费商用，无需注明来源</li>
                <li><strong>Freepik：</strong>免费使用需注明来源，或购买付费版本</li>
                <li><strong>OpenClipart：</strong>公共域，完全免费使用</li>
                <li><strong>Flaticon：</strong>免费使用需注明来源</li>
            </ul>
        </div>
    </div>
</body>
</html>
