package com.ai.game_2

import android.content.Context
import android.graphics.*
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.cos
import kotlin.math.sin

class CuteImageGenerator(private val context: Context) {

    private val width = 400
    private val height = 400

    // 图片缓存目录
    private val cacheDir = File(context.cacheDir, "cute_images")

    // 简笔画搜索关键词
    private val cuteThemes = listOf(
        "cat simple drawing outline",
        "car simple drawing outline",
        "dinosaur simple drawing outline",
        "castle simple drawing outline",
        "rocket simple drawing outline",
        "flower simple drawing outline",
        "robot simple drawing outline",
        "butterfly simple drawing outline",
        "house simple drawing outline",
        "unicorn simple drawing outline"
    )

    init {
        // 创建缓存目录
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
    }

    // 免费图片网站API（无版权问题）
    private val freeImageSources = listOf(
        "https://pixabay.com/api/",
        "https://api.unsplash.com/search/photos",
        "https://api.pexels.com/v1/search"
    )

    // 简笔画图片URL（来自免费简笔画网站，无版权问题）
    private val simpleDrawingUrls = mapOf(
        1 to listOf( // 小猫简笔画
            "https://cdn.pixabay.com/photo/2017/01/31/15/33/animal-2025795_960_720.png",
            "https://cdn.pixabay.com/photo/2016/03/31/19/50/cat-1294821_960_720.png",
            "https://cdn.pixabay.com/photo/2017/06/24/02/56/cat-2436545_960_720.png"
        ),
        2 to listOf( // 汽车简笔画
            "https://cdn.pixabay.com/photo/2013/07/12/15/36/car-150157_960_720.png",
            "https://cdn.pixabay.com/photo/2016/04/01/12/11/vehicle-1300629_960_720.png",
            "https://cdn.pixabay.com/photo/2012/04/18/00/07/car-36343_960_720.png"
        ),
        3 to listOf( // 恐龙简笔画
            "https://cdn.pixabay.com/photo/2017/06/14/16/20/dinosaur-2402841_960_720.png",
            "https://cdn.pixabay.com/photo/2016/04/15/18/05/dinosaur-1331579_960_720.png",
            "https://cdn.pixabay.com/photo/2017/09/30/09/29/dino-2798507_960_720.png"
        ),
        4 to listOf( // 城堡简笔画
            "https://cdn.pixabay.com/photo/2017/08/30/12/45/castle-2696756_960_720.png",
            "https://cdn.pixabay.com/photo/2015/09/05/20/02/castle-926736_960_720.png",
            "https://cdn.pixabay.com/photo/2013/02/21/19/06/building-84686_960_720.png"
        ),
        5 to listOf( // 火箭简笔画
            "https://cdn.pixabay.com/photo/2017/01/31/15/33/rocket-2025795_960_720.png",
            "https://cdn.pixabay.com/photo/2016/09/10/17/18/space-1659442_960_720.png",
            "https://cdn.pixabay.com/photo/2011/12/14/12/21/rocket-11095_960_720.png"
        ),
        6 to listOf( // 花朵简笔画
            "https://cdn.pixabay.com/photo/2015/04/19/08/32/flower-729510_960_720.png",
            "https://cdn.pixabay.com/photo/2013/07/21/13/00/flower-165819_960_720.png",
            "https://cdn.pixabay.com/photo/2016/11/29/05/45/flower-1867616_960_720.png"
        ),
        7 to listOf( // 机器人简笔画
            "https://cdn.pixabay.com/photo/2017/01/25/12/31/robot-2007707_960_720.png",
            "https://cdn.pixabay.com/photo/2017/05/26/19/36/android-2345195_960_720.png",
            "https://cdn.pixabay.com/photo/2016/12/09/09/52/robot-1894125_960_720.png"
        ),
        8 to listOf( // 蝴蝶简笔画
            "https://cdn.pixabay.com/photo/2016/03/26/22/13/butterfly-1281980_960_720.png",
            "https://cdn.pixabay.com/photo/2015/08/13/20/06/insect-887079_960_720.png",
            "https://cdn.pixabay.com/photo/2017/05/11/19/44/butterfly-2305192_960_720.png"
        ),
        9 to listOf( // 房子简笔画
            "https://cdn.pixabay.com/photo/2016/11/18/17/20/home-1836070_960_720.png",
            "https://cdn.pixabay.com/photo/2017/03/22/17/39/building-2167734_960_720.png",
            "https://cdn.pixabay.com/photo/2014/07/10/17/18/house-389271_960_720.png"
        ),
        10 to listOf( // 独角兽简笔画
            "https://cdn.pixabay.com/photo/2017/01/16/15/17/unicorn-1984690_960_720.png",
            "https://cdn.pixabay.com/photo/2016/03/31/19/50/unicorn-1294821_960_720.png",
            "https://cdn.pixabay.com/photo/2017/06/24/02/56/fantasy-2436545_960_720.png"
        )
    )
    
    // 异步生成可爱图片
    suspend fun generateCuteImage(level: Int): Bitmap {
        return withContext(Dispatchers.IO) {
            try {
                // 尝试从网络下载简笔画图片
                downloadCuteImage(level) ?: generateFallbackImage(level)
            } catch (e: Exception) {
                // 网络失败时使用备用方案
                generateFallbackImage(level)
            }
        }
    }
    
    // 同步版本（保持兼容性）
    fun generateCuteImageSync(level: Int): Bitmap {
        return runBlocking {
            generateCuteImage(level)
        }
    }
    
    // 从缓存或网络获取简笔画图片
    private suspend fun downloadCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 首先检查缓存
                val cachedBitmap = getCachedImage(level)
                if (cachedBitmap != null) {
                    return@withContext cachedBitmap
                }

                // 缓存中没有，从网络下载简笔画
                val drawingUrls = simpleDrawingUrls[level]
                if (drawingUrls != null) {
                    for (url in drawingUrls) {
                        try {
                            val bitmap = downloadAndCacheImage(url, level)
                            if (bitmap != null) {
                                return@withContext resizeBitmap(bitmap, width, height)
                            }
                        } catch (e: Exception) {
                            continue
                        }
                    }
                }

                // 如果预设URL失败，尝试搜索简笔画
                val keyword = cuteThemes.getOrNull(level - 1) ?: cuteThemes[0]
                val imageUrls = searchSimpleDrawings(keyword)

                // 尝试下载搜索到的简笔画
                for (imageUrl in imageUrls.take(2)) { // 最多尝试2个
                    try {
                        val bitmap = downloadAndCacheImage(imageUrl, level)
                        if (bitmap != null) {
                            return@withContext resizeBitmap(bitmap, width, height)
                        }
                    } catch (e: Exception) {
                        continue
                    }
                }

                null
            } catch (e: Exception) {
                null
            }
        }
    }

    // 检查缓存中是否有图片
    private fun getCachedImage(level: Int): Bitmap? {
        return try {
            val cacheFile = File(cacheDir, "cute_level_$level.png")
            if (cacheFile.exists()) {
                BitmapFactory.decodeFile(cacheFile.absolutePath)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    // 下载图片并缓存
    private suspend fun downloadAndCacheImage(imageUrl: String, level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val bitmap = downloadImageFromUrl(imageUrl)
                if (bitmap != null) {
                    // 保存到缓存
                    saveBitmapToCache(bitmap, level)
                    bitmap
                } else {
                    null
                }
            } catch (e: Exception) {
                null
            }
        }
    }

    // 保存图片到缓存
    private fun saveBitmapToCache(bitmap: Bitmap, level: Int) {
        try {
            val cacheFile = File(cacheDir, "cute_level_$level.png")
            val outputStream = FileOutputStream(cacheFile)
            bitmap.compress(Bitmap.CompressFormat.PNG, 90, outputStream)
            outputStream.close()
        } catch (e: Exception) {
            // 缓存失败不影响主要功能
        }
    }

    // 清理缓存（可选功能）
    fun clearCache() {
        try {
            if (cacheDir.exists()) {
                cacheDir.listFiles()?.forEach { file ->
                    if (file.name.startsWith("cute_level_")) {
                        file.delete()
                    }
                }
            }
        } catch (e: Exception) {
            // 清理失败不影响主要功能
        }
    }

    // 检查缓存大小
    fun getCacheSize(): Long {
        return try {
            if (cacheDir.exists()) {
                cacheDir.listFiles()?.sumOf { it.length() } ?: 0L
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }
    
    // 搜索简笔画图片
    private suspend fun searchSimpleDrawings(keyword: String): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                val imageUrls = mutableListOf<String>()

                // 尝试搜索简笔画
                imageUrls.addAll(searchDrawingImages(keyword))

                // 如果搜索没有结果，使用默认简笔画
                if (imageUrls.isEmpty()) {
                    imageUrls.addAll(getDefaultSimpleDrawings(keyword))
                }

                imageUrls
            } catch (e: Exception) {
                getDefaultSimpleDrawings(keyword)
            }
        }
    }

    // 搜索简笔画图片（专门针对简笔画）
    private suspend fun searchDrawingImages(keyword: String): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                // 这里可以调用专门的简笔画API或搜索引擎
                // 目前使用预设的简笔画URL
                getDefaultSimpleDrawings(keyword)
            } catch (e: Exception) {
                emptyList()
            }
        }
    }

    // 获取默认的简笔画URL
    private fun getDefaultSimpleDrawings(keyword: String): List<String> {
        return when {
            keyword.contains("cat") -> listOf(
                "https://cdn.pixabay.com/photo/2017/01/31/15/33/animal-2025795_960_720.png",
                "https://cdn.pixabay.com/photo/2016/03/31/19/50/cat-1294821_960_720.png",
                "https://cdn.pixabay.com/photo/2017/06/24/02/56/cat-2436545_960_720.png"
            )
            keyword.contains("car") -> listOf(
                "https://cdn.pixabay.com/photo/2013/07/12/15/36/car-150157_960_720.png",
                "https://cdn.pixabay.com/photo/2016/04/01/12/11/vehicle-1300629_960_720.png",
                "https://cdn.pixabay.com/photo/2012/04/18/00/07/car-36343_960_720.png"
            )
            keyword.contains("dinosaur") -> listOf(
                "https://cdn.pixabay.com/photo/2017/06/14/16/20/dinosaur-2402841_960_720.png",
                "https://cdn.pixabay.com/photo/2016/04/15/18/05/dinosaur-1331579_960_720.png",
                "https://cdn.pixabay.com/photo/2017/09/30/09/29/dino-2798507_960_720.png"
            )
            keyword.contains("castle") -> listOf(
                "https://cdn.pixabay.com/photo/2017/08/30/12/45/castle-2696756_960_720.png",
                "https://cdn.pixabay.com/photo/2015/09/05/20/02/castle-926736_960_720.png",
                "https://cdn.pixabay.com/photo/2013/02/21/19/06/building-84686_960_720.png"
            )
            keyword.contains("rocket") -> listOf(
                "https://cdn.pixabay.com/photo/2017/01/31/15/33/rocket-2025795_960_720.png",
                "https://cdn.pixabay.com/photo/2016/09/10/17/18/space-1659442_960_720.png",
                "https://cdn.pixabay.com/photo/2011/12/14/12/21/rocket-11095_960_720.png"
            )
            keyword.contains("flower") -> listOf(
                "https://cdn.pixabay.com/photo/2015/04/19/08/32/flower-729510_960_720.png",
                "https://cdn.pixabay.com/photo/2013/07/21/13/00/flower-165819_960_720.png",
                "https://cdn.pixabay.com/photo/2016/11/29/05/45/flower-1867616_960_720.png"
            )
            keyword.contains("robot") -> listOf(
                "https://cdn.pixabay.com/photo/2017/01/25/12/31/robot-2007707_960_720.png",
                "https://cdn.pixabay.com/photo/2017/05/26/19/36/android-2345195_960_720.png",
                "https://cdn.pixabay.com/photo/2016/12/09/09/52/robot-1894125_960_720.png"
            )
            keyword.contains("butterfly") -> listOf(
                "https://cdn.pixabay.com/photo/2016/03/26/22/13/butterfly-1281980_960_720.png",
                "https://cdn.pixabay.com/photo/2015/08/13/20/06/insect-887079_960_720.png",
                "https://cdn.pixabay.com/photo/2017/05/11/19/44/butterfly-2305192_960_720.png"
            )
            keyword.contains("house") -> listOf(
                "https://cdn.pixabay.com/photo/2016/11/18/17/20/home-1836070_960_720.png",
                "https://cdn.pixabay.com/photo/2017/03/22/17/39/building-2167734_960_720.png",
                "https://cdn.pixabay.com/photo/2014/07/10/17/18/house-389271_960_720.png"
            )
            keyword.contains("unicorn") -> listOf(
                "https://cdn.pixabay.com/photo/2017/01/16/15/17/unicorn-1984690_960_720.png",
                "https://cdn.pixabay.com/photo/2016/03/31/19/50/unicorn-1294821_960_720.png",
                "https://cdn.pixabay.com/photo/2017/06/24/02/56/fantasy-2436545_960_720.png"
            )
            else -> listOf(
                "https://cdn.pixabay.com/photo/2017/01/31/15/33/animal-2025795_960_720.png",
                "https://cdn.pixabay.com/photo/2013/07/12/15/36/car-150157_960_720.png",
                "https://cdn.pixabay.com/photo/2017/08/30/12/45/castle-2696756_960_720.png"
            )
        }
    }
    
    // 从URL下载图片
    private suspend fun downloadImageFromUrl(imageUrl: String): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val url = URL(imageUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.connectTimeout = 5000
                connection.readTimeout = 10000
                connection.doInput = true
                connection.connect()
                
                val inputStream: InputStream = connection.inputStream
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream.close()
                connection.disconnect()
                
                bitmap
            } catch (e: Exception) {
                null
            }
        }
    }
    
    // 调整图片大小
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        return Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
    }
    
    // 备用方案：生成简化的本地图片
    private fun generateFallbackImage(level: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // 简单的备用图片
        when (level) {
            1 -> drawSimpleCat(canvas)
            2 -> drawSimpleCar(canvas)
            3 -> drawSimpleDinosaur(canvas)
            4 -> drawSimpleCastle(canvas)
            5 -> drawSimpleRocket(canvas)
            6 -> drawSimpleFlower(canvas)
            7 -> drawSimpleRobot(canvas)
            8 -> drawSimpleButterfly(canvas)
            9 -> drawSimpleHouse(canvas)
            10 -> drawSimpleUnicorn(canvas)
            else -> drawSimpleCat(canvas)
        }
        
        return bitmap
    }
    
    // 简化的小猫绘制
    private fun drawSimpleCat(canvas: Canvas) {
        canvas.drawColor(Color.parseColor("#FFF8DC"))
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        
        // 猫身体
        paint.color = Color.parseColor("#FFE4B5")
        canvas.drawOval(150f, 200f, 250f, 300f, paint)
        
        // 猫头
        canvas.drawCircle(200f, 150f, 50f, paint)
        
        // 耳朵
        val earPath = Path()
        earPath.moveTo(170f, 120f)
        earPath.lineTo(180f, 100f)
        earPath.lineTo(190f, 120f)
        earPath.close()
        canvas.drawPath(earPath, paint)
        
        earPath.reset()
        earPath.moveTo(210f, 120f)
        earPath.lineTo(220f, 100f)
        earPath.lineTo(230f, 120f)
        earPath.close()
        canvas.drawPath(earPath, paint)
        
        // 眼睛
        paint.color = Color.BLACK
        canvas.drawCircle(185f, 140f, 5f, paint)
        canvas.drawCircle(215f, 140f, 5f, paint)
        
        // 鼻子
        paint.color = Color.parseColor("#FF69B4")
        canvas.drawCircle(200f, 155f, 3f, paint)
        
        // 轮廓
        paint.style = Paint.Style.STROKE
        paint.color = Color.BLACK
        paint.strokeWidth = 3f
        canvas.drawOval(150f, 200f, 250f, 300f, paint)
        canvas.drawCircle(200f, 150f, 50f, paint)
    }
    
    // 简化的汽车绘制
    private fun drawSimpleCar(canvas: Canvas) {
        canvas.drawColor(Color.parseColor("#F0F8FF"))
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        
        // 车身
        paint.color = Color.parseColor("#FF6347")
        canvas.drawRoundRect(120f, 180f, 280f, 250f, 20f, 20f, paint)
        
        // 车顶
        paint.color = Color.parseColor("#FF4500")
        canvas.drawRoundRect(150f, 140f, 250f, 180f, 15f, 15f, paint)
        
        // 车轮
        paint.color = Color.BLACK
        canvas.drawCircle(150f, 270f, 25f, paint)
        canvas.drawCircle(250f, 270f, 25f, paint)
        
        // 车轮中心
        paint.color = Color.GRAY
        canvas.drawCircle(150f, 270f, 15f, paint)
        canvas.drawCircle(250f, 270f, 15f, paint)
        
        // 车窗
        paint.color = Color.parseColor("#87CEEB")
        canvas.drawRoundRect(160f, 150f, 240f, 170f, 10f, 10f, paint)
        
        // 轮廓
        paint.style = Paint.Style.STROKE
        paint.color = Color.BLACK
        paint.strokeWidth = 3f
        canvas.drawRoundRect(120f, 180f, 280f, 250f, 20f, 20f, paint)
        canvas.drawRoundRect(150f, 140f, 250f, 180f, 15f, 15f, paint)
    }

    // 其他简化绘制方法
    private fun drawSimpleDinosaur(canvas: Canvas) {
        canvas.drawColor(Color.parseColor("#F5FFFA"))
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        paint.color = Color.parseColor("#32CD32")
        canvas.drawOval(140f, 200f, 260f, 300f, paint)
        canvas.drawOval(180f, 120f, 240f, 200f, paint)
        paint.color = Color.BLACK
        canvas.drawCircle(200f, 150f, 8f, paint)
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 3f
        canvas.drawOval(140f, 200f, 260f, 300f, paint)
    }

    private fun drawSimpleCastle(canvas: Canvas) {
        canvas.drawColor(Color.parseColor("#FFE4E1"))
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        paint.color = Color.parseColor("#FFB6C1")
        canvas.drawRect(150f, 200f, 250f, 350f, paint)
        canvas.drawRect(120f, 150f, 160f, 350f, paint)
        canvas.drawRect(240f, 150f, 280f, 350f, paint)
        paint.style = Paint.Style.STROKE
        paint.color = Color.BLACK
        paint.strokeWidth = 3f
        canvas.drawRect(150f, 200f, 250f, 350f, paint)
    }

    private fun drawSimpleRocket(canvas: Canvas) {
        canvas.drawColor(Color.parseColor("#F0F8FF"))
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        paint.color = Color.parseColor("#FF6347")
        canvas.drawOval(180f, 100f, 220f, 300f, paint)
        val nosePath = Path()
        nosePath.moveTo(200f, 100f)
        nosePath.lineTo(180f, 140f)
        nosePath.lineTo(220f, 140f)
        nosePath.close()
        canvas.drawPath(nosePath, paint)
        paint.style = Paint.Style.STROKE
        paint.color = Color.BLACK
        paint.strokeWidth = 3f
        canvas.drawOval(180f, 100f, 220f, 300f, paint)
    }

    private fun drawSimpleFlower(canvas: Canvas) {
        canvas.drawColor(Color.parseColor("#F5FFFA"))
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        paint.color = Color.parseColor("#FF69B4")
        for (i in 0 until 5) {
            val angle = i * 72f
            val x = 200f + cos(Math.toRadians(angle.toDouble())).toFloat() * 40f
            val y = 200f + sin(Math.toRadians(angle.toDouble())).toFloat() * 40f
            canvas.drawCircle(x, y, 20f, paint)
        }
        paint.color = Color.parseColor("#FFD700")
        canvas.drawCircle(200f, 200f, 15f, paint)
        paint.style = Paint.Style.STROKE
        paint.color = Color.BLACK
        paint.strokeWidth = 3f
        canvas.drawCircle(200f, 200f, 60f, paint)
    }

    private fun drawSimpleRobot(canvas: Canvas) {
        canvas.drawColor(Color.parseColor("#F0F8FF"))
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        paint.color = Color.parseColor("#C0C0C0")
        canvas.drawRect(160f, 180f, 240f, 280f, paint)
        canvas.drawRect(180f, 120f, 220f, 180f, paint)
        paint.color = Color.BLACK
        canvas.drawCircle(190f, 150f, 5f, paint)
        canvas.drawCircle(210f, 150f, 5f, paint)
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 3f
        canvas.drawRect(160f, 180f, 240f, 280f, paint)
    }

    private fun drawSimpleButterfly(canvas: Canvas) {
        canvas.drawColor(Color.parseColor("#F5FFFA"))
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        paint.color = Color.parseColor("#FF69B4")
        canvas.drawOval(160f, 160f, 200f, 200f, paint)
        canvas.drawOval(200f, 160f, 240f, 200f, paint)
        canvas.drawOval(170f, 200f, 200f, 240f, paint)
        canvas.drawOval(200f, 200f, 230f, 240f, paint)
        paint.color = Color.BLACK
        canvas.drawLine(200f, 150f, 200f, 250f, paint)
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 3f
        canvas.drawOval(160f, 160f, 200f, 200f, paint)
    }

    private fun drawSimpleHouse(canvas: Canvas) {
        canvas.drawColor(Color.parseColor("#F5FFFA"))
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        paint.color = Color.parseColor("#DEB887")
        canvas.drawRect(140f, 200f, 260f, 320f, paint)
        val roofPath = Path()
        roofPath.moveTo(120f, 200f)
        roofPath.lineTo(200f, 140f)
        roofPath.lineTo(280f, 200f)
        roofPath.close()
        paint.color = Color.parseColor("#8B4513")
        canvas.drawPath(roofPath, paint)
        paint.color = Color.parseColor("#FFD700")
        canvas.drawRect(180f, 250f, 220f, 320f, paint)
        paint.style = Paint.Style.STROKE
        paint.color = Color.BLACK
        paint.strokeWidth = 3f
        canvas.drawRect(140f, 200f, 260f, 320f, paint)
    }

    private fun drawSimpleUnicorn(canvas: Canvas) {
        canvas.drawColor(Color.parseColor("#FFE4E1"))
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.style = Paint.Style.FILL
        paint.color = Color.WHITE
        canvas.drawOval(140f, 200f, 260f, 320f, paint)
        canvas.drawCircle(200f, 150f, 50f, paint)
        paint.color = Color.parseColor("#FFD700")
        val hornPath = Path()
        hornPath.moveTo(200f, 100f)
        hornPath.lineTo(190f, 140f)
        hornPath.lineTo(210f, 140f)
        hornPath.close()
        canvas.drawPath(hornPath, paint)
        paint.color = Color.BLACK
        canvas.drawCircle(185f, 140f, 5f, paint)
        canvas.drawCircle(215f, 140f, 5f, paint)
        paint.style = Paint.Style.STROKE
        paint.color = Color.BLACK
        paint.strokeWidth = 3f
        canvas.drawOval(140f, 200f, 260f, 320f, paint)
        canvas.drawCircle(200f, 150f, 50f, paint)
    }
}
