package com.ai.game_2

import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity

class VoicePuzzleActivity : AppCompatActivity() {
    
    private lateinit var puzzleView: PuzzleView
    private lateinit var levelText: TextView
    private lateinit var difficultyText: TextView
    private lateinit var backButton: Button
    private lateinit var resetButton: Button
    
    private var currentLevel: Int = 1
    private var searchQuery: String = ""
    private var imageUrls: ArrayList<String> = arrayListOf()
    private var useDynamicGrid: Boolean = false
    private lateinit var sharedPreferences: SharedPreferences
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_puzzle_game)
        
        sharedPreferences = getSharedPreferences("PuzzleGame", MODE_PRIVATE)
        
        // 获取语音搜索参数
        searchQuery = intent.getStringExtra("search_query") ?: ""
        imageUrls = intent.getStringArrayListExtra("image_urls") ?: arrayListOf()
        currentLevel = intent.getIntExtra("level", 1)
        useDynamicGrid = intent.getBooleanExtra("useDynamicGrid", false)
        
        initializeViews()
        setupGame()
        setupClickListeners()
    }
    
    private fun initializeViews() {
        puzzleView = findViewById(R.id.puzzle_view)
        levelText = findViewById(R.id.tv_level)
        difficultyText = findViewById(R.id.tv_difficulty)
        backButton = findViewById(R.id.btn_back)
        resetButton = findViewById(R.id.btn_reset)
    }
    
    private fun setupGame() {
        if (imageUrls.isEmpty() || currentLevel > imageUrls.size) {
            finish()
            return
        }
        
        levelText.text = "第 $currentLevel 关"

        // 使用对应的图片URL初始化拼图
        val imageUrl = imageUrls[currentLevel - 1]

        // 使用真实的网络图片URL加载拼图
        puzzleView.initializePuzzleWithUrl(
            imageUrl = imageUrl,
            onComplete = {
                // 图片加载完成后更新难度显示
                updateDifficultyText()
            },
            onReferenceReady = null // VoicePuzzleActivity可能没有参考图，所以传null
        )
        
        puzzleView.onPuzzleCompleted = {
            onPuzzleCompleted()
        }
    }

    private fun updateDifficultyText() {
        val gridInfo = if (useDynamicGrid) {
            "${puzzleView.gridCols}x${puzzleView.gridRows} (${puzzleView.gridSize}块)"
        } else {
            "3x3 (9块)"
        }
        difficultyText.text = "$searchQuery - 图片 $currentLevel ($gridInfo)"
    }
    
    private fun setupClickListeners() {
        backButton.setOnClickListener {
            finish()
        }
        
        resetButton.setOnClickListener {
            puzzleView.resetPuzzle()
        }
    }
    
    private fun onPuzzleCompleted() {
        saveProgress()
        showCompletionDialog()
    }
    
    private fun saveProgress() {
        val editor = sharedPreferences.edit()
        val maxUnlockedLevelKey = "max_unlocked_level_VOICE_SEARCH"
        val maxUnlockedLevel = sharedPreferences.getInt(maxUnlockedLevelKey, 1)
        
        if (currentLevel >= maxUnlockedLevel) {
            editor.putInt(maxUnlockedLevelKey, currentLevel + 1)
        }
        
        editor.putBoolean("level_VOICE_SEARCH_${currentLevel}_completed", true)
        editor.apply()
    }
    
    private fun showCompletionDialog() {
        val isLastLevel = currentLevel >= imageUrls.size

        val message = if (isLastLevel) {
            "恭喜！你完成了所有 ${imageUrls.size} 个关卡！\n搜索词：$searchQuery"
        } else {
            "太棒了！你完成了第 $currentLevel 关！\n下一关已解锁，可以继续挑战！"
        }

        val builder = AlertDialog.Builder(this)
            .setTitle("关卡完成！🎉")
            .setMessage(message)
            .setCancelable(false)

        builder.setPositiveButton("关闭") { dialog, _ ->
            dialog.dismiss()
        }

        builder.show()
    }
    

}
