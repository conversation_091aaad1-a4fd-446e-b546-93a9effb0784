# 🎯 拼图难度系统实现说明

## 📋 功能概述

实现了三个难度等级的拼图系统：**简单**、**中等**、**困难**，所有拼图主题都按照统一的难度逻辑进行网格计算。

## 🎮 难度等级设计

### 三个难度等级
- 🟢 **简单 (EASY)**：基础网格 2×2，适合初学者
- 🔵 **中等 (MEDIUM)**：基础网格 3×3，适中难度  
- 🔴 **困难 (HARD)**：基础网格 4×4，挑战高手

### 智能网格计算
```
基础规则：
- 正方形图片：使用基础网格 (2×2, 3×3, 4×4)
- 横向图片：列数 = 基础网格 + 1，行数 = 基础网格
- 纵向图片：列数 = 基础网格，行数 = 基础网格 + 1

示例 (中等难度)：
- 正方形图片：3×3 = 9块
- 横向图片：4×3 = 12块  
- 纵向图片：3×4 = 12块
```

## 🏗️ 技术架构

### 1. 核心类设计

#### DifficultyLevel (枚举)
```kotlin
enum class DifficultyLevel(
    val displayName: String,
    val baseGridSize: Int,
    val description: String
) {
    EASY("简单", 2, "2×2 格子，适合初学者"),
    MEDIUM("中等", 3, "3×3 格子，适中难度"),
    HARD("困难", 4, "4×4 格子，挑战高手")
}
```

#### DifficultyManager (管理器)
```kotlin
object DifficultyManager {
    // 根据难度和图片比例计算网格
    fun calculateGridSize(difficulty: DifficultyLevel, imageWidth: Int, imageHeight: Int): Pair<Int, Int>
    
    // 获取正方形网格尺寸
    fun getSquareGridSize(difficulty: DifficultyLevel): Pair<Int, Int>
    
    // 生成难度描述文本
    fun getDifficultyDescription(difficulty: DifficultyLevel, cols: Int, rows: Int): String
}
```

### 2. 用户界面

#### DifficultySelectActivity
- 📱 **难度选择界面**：显示三个难度选项
- 🎨 **卡片式设计**：每个难度用独立卡片展示
- 📊 **详细信息**：显示基础网格、总块数、描述
- 🎯 **颜色区分**：简单(绿色)、中等(蓝色)、困难(红色)

#### 布局文件
- `activity_difficulty_select.xml`：难度选择主界面
- `item_difficulty.xml`：单个难度选项卡片

### 3. 游戏流程更新

#### 新的游戏流程
```
1. 主题选择 → 2. 关卡选择 → 3. 难度选择 → 4. 开始拼图
```

#### 原有流程对比
```
旧流程：主题选择 → 关卡选择 → 开始拼图 (固定3×3)
新流程：主题选择 → 关卡选择 → 难度选择 → 开始拼图 (动态网格)
```

## 🔧 实现细节

### 1. PuzzleView 更新
```kotlin
class PuzzleView {
    var currentDifficulty: DifficultyLevel = DifficultyLevel.getDefault()
    
    fun setDifficulty(difficulty: DifficultyLevel) {
        currentDifficulty = difficulty
    }
    
    private fun calculateDynamicGrid(imageWidth: Int, imageHeight: Int) {
        val gridDimensions = DifficultyManager.calculateGridSize(currentDifficulty, imageWidth, imageHeight)
        gridCols = gridDimensions.first
        gridRows = gridDimensions.second
    }
}
```

### 2. PuzzleGameActivity 更新
```kotlin
class PuzzleGameActivity {
    private var currentDifficulty: DifficultyLevel = DifficultyLevel.getDefault()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        // 获取难度参数
        val difficultyName = intent.getStringExtra("difficulty") ?: DifficultyLevel.getDefault().name
        currentDifficulty = DifficultyLevel.fromName(difficultyName)
        
        // 设置PuzzleView的难度
        puzzleView.setDifficulty(currentDifficulty)
    }
}
```

### 3. 导航流程更新
```kotlin
// LevelSelectActivity → DifficultySelectActivity
private fun startGame(theme: GameTheme, level: Int) {
    val intent = Intent(this, DifficultySelectActivity::class.java)
    intent.putExtra("theme", theme)
    intent.putExtra("level", level)
    startActivity(intent)
}

// DifficultySelectActivity → PuzzleGameActivity  
private fun startPuzzleGame(difficulty: DifficultyLevel) {
    val intent = Intent(this, PuzzleGameActivity::class.java)
    intent.putExtra("difficulty", difficulty.name)
    // ... 其他参数
    startActivity(intent)
}
```

## 🎨 用户体验

### 1. 难度选择界面
- 📋 **清晰的标题**："选择难度等级"
- 💡 **使用提示**："网格大小会根据图片比例自动调整"
- 🎯 **推荐提示**："初学者建议选择简单难度"

### 2. 难度信息展示
```
简单难度卡片：
┌─────────────────────┐
│ 简单 (绿色)         │
│ 2×2 格子，适合初学者 │
│ 基础网格: 2×2 (4块) │
│ 实际网格会根据图片   │
│ 比例自动调整        │
│ [选择此难度]        │
└─────────────────────┘
```

### 3. 游戏内显示
- 🏷️ **难度标签**：在游戏界面显示当前难度
- 📊 **网格信息**：显示实际网格尺寸和总块数
- 🎯 **动态描述**："中等 3×4 (12块)"

## 🌟 支持的主题

### 全主题支持
- ✅ **可爱主题**：使用难度系统的正方形网格
- ✅ **语音搜图主题**：根据图片比例和难度动态计算
- ✅ **拍照主题**：根据照片比例和难度动态计算
- ✅ **其他主题**：统一使用难度系统

### 网格计算示例
```
中等难度 (基础3×3)：
- 正方形照片：3×3 = 9块
- 横向照片：4×3 = 12块
- 纵向照片：3×4 = 12块

困难难度 (基础4×4)：
- 正方形照片：4×4 = 16块
- 横向照片：5×4 = 20块
- 纵向照片：4×5 = 20块
```

## 📱 兼容性保证

### 向后兼容
- ✅ **默认难度**：未指定难度时使用中等难度
- ✅ **现有功能**：所有原有功能正常工作
- ✅ **参数传递**：完整传递所有主题的特殊参数

### 错误处理
- 🛡️ **参数验证**：检查网格尺寸合理性
- 🔄 **自动降级**：异常时使用默认难度
- 📝 **详细日志**：记录难度设置和网格计算过程

## 🎯 用户收益

### 1. 个性化体验
- 🎮 **自由选择**：根据技能水平选择合适难度
- 📈 **渐进提升**：从简单到困难逐步挑战
- 🏆 **成就感**：完成高难度获得更大满足感

### 2. 游戏性提升
- 🔄 **重复游戏性**：同一关卡可以尝试不同难度
- 🎯 **挑战性**：困难模式提供更大挑战
- 👶 **易用性**：简单模式降低入门门槛

### 3. 智能适配
- 📐 **比例适配**：网格自动适应图片比例
- 🎨 **视觉优化**：保持拼图的视觉连贯性
- ⚡ **性能优化**：合理的拼图块数量

这个难度系统为所有拼图主题提供了统一、智能、用户友好的难度选择体验！🎉
