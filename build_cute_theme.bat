@echo off
echo Building Cute Theme Puzzle Game...
echo.

REM 设置日期
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "datestamp=%YYYY%%MM%%DD%"

echo Build date: %datestamp%
echo Testing Cute Theme functionality...
echo.

REM 尝试使用Android Studio的gradle
if exist "%LOCALAPPDATA%\Android\Sdk\tools\bin\sdkmanager.bat" (
    echo Found Android SDK
    set "ANDROID_HOME=%LOCALAPPDATA%\Android\Sdk"
)

REM 尝试找到Java
for /d %%i in ("%ProgramFiles%\Android\Android Studio\jbr*") do (
    if exist "%%i\bin\java.exe" (
        set "JAVA_HOME=%%i"
        echo Found Java at: %%i
        goto :build
    )
)

for /d %%i in ("%ProgramFiles%\Java\jdk*") do (
    if exist "%%i\bin\java.exe" (
        set "JAVA_HOME=%%i"
        echo Found Java at: %%i
        goto :build
    )
)

echo Java not found. Please install Android Studio or set JAVA_HOME
echo You can also try building in Android Studio directly
pause
exit /b 1

:build
echo.
echo Building APK with Cute Theme support...
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful!
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        copy "app\build\outputs\apk\debug\app-debug.apk" "PuzzleGame_CuteTheme_%datestamp%.apk"
        echo APK saved as: PuzzleGame_CuteTheme_%datestamp%.apk
    )
) else (
    echo.
    echo Build failed. Please check the errors above.
)

pause
