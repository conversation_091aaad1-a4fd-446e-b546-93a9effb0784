package com.ai.game_2

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy

class DownloadedImageAdapter(
    private val imageUrls: List<String>
) : RecyclerView.Adapter<DownloadedImageAdapter.ImageViewHolder>() {
    
    class ImageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val imageView: ImageView = itemView.findViewById(R.id.iv_downloaded_image)
        val indexText: TextView = itemView.findViewById(R.id.tv_image_index)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ImageViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_downloaded_image, parent, false)
        return ImageViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ImageViewHolder, position: Int) {
        val imageUrl = imageUrls[position]

        // 显示图片索引
        holder.indexText.text = "图片 ${position + 1}"

        // 检查是否是默认图片或语音搜索预览图
        if (imageUrl.startsWith("default_image_") || imageUrl.startsWith("voice_search_placeholder_")) {
            // 显示默认图片
            holder.imageView.setImageResource(R.drawable.ic_voice_placeholder)
        } else {
            // 使用Glide加载真实的网络图片
            try {
                Glide.with(holder.itemView.context)
                    .load(imageUrl)
                    .placeholder(R.drawable.ic_voice_placeholder)
                    .error(R.drawable.ic_voice_placeholder)
                    .diskCacheStrategy(DiskCacheStrategy.DATA)
                    .timeout(10000) // 10秒超时
                    .centerCrop()
                    .into(holder.imageView)
            } catch (e: Exception) {
                // 如果Glide加载失败，显示占位符
                holder.imageView.setImageResource(R.drawable.ic_voice_placeholder)
            }
        }
    }
    
    override fun getItemCount(): Int = imageUrls.size
}
