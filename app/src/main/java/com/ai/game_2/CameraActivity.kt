package com.ai.game_2

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.widget.*
import android.view.View
import android.widget.AdapterView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

class CameraActivity : AppCompatActivity() {

    private lateinit var btnTakePhoto: Button
    private lateinit var btnStartPuzzle: Button
    private lateinit var btnBack: Button
    private lateinit var tvPhotoCount: TextView
    private lateinit var rvPhotos: RecyclerView
    private lateinit var photoAdapter: PhotoAdapter
    private lateinit var gridInfoText: TextView

    private val photoList = mutableListOf<String>()
    private var currentPhotoPath: String? = null
    
    companion object {
        private const val REQUEST_IMAGE_CAPTURE = 1
        private const val REQUEST_CAMERA_PERMISSION = 2
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_camera)

        initializeViews()
        setupRecyclerView()
        setupClickListeners()
        checkCameraPermission()
    }
    
    private fun initializeViews() {
        btnTakePhoto = findViewById(R.id.btn_take_photo)
        btnStartPuzzle = findViewById(R.id.btn_start_puzzle)
        btnBack = findViewById(R.id.btn_back)
        tvPhotoCount = findViewById(R.id.tv_photo_count)
        rvPhotos = findViewById(R.id.rv_photos)
        gridSizeSpinner = findViewById(R.id.spinner_grid_size)
        gridInfoText = findViewById(R.id.tv_grid_info)

        setupGridSizeSpinner()
        updateUI()
    }

    private fun setupRecyclerView() {
        photoAdapter = PhotoAdapter(photoList) { position ->
            // 删除照片
            removePhoto(position)
        }
        rvPhotos.layoutManager = GridLayoutManager(this, 3)
        rvPhotos.adapter = photoAdapter
    }
    
    private fun setupClickListeners() {
        btnTakePhoto.setOnClickListener {
            if (checkCameraPermission()) {
                dispatchTakePictureIntent()
            }
        }

        btnStartPuzzle.setOnClickListener {
            startPuzzleGame()
        }

        btnBack.setOnClickListener {
            finish()
        }
    }

    private fun setupGridSizeSpinner() {
        val gridOptions = arrayOf("3格", "4格", "5格", "6格")
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, gridOptions)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        gridSizeSpinner.adapter = adapter

        // 默认选择3格
        gridSizeSpinner.setSelection(0)
        selectedGridCols = 3

        gridSizeSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                selectedGridCols = position + 3  // 3, 4, 5, 6
                updateGridInfo()
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        updateGridInfo()
    }

    private fun updateGridInfo() {
        gridInfoText.text = "横格：${selectedGridCols}格，纵格将根据照片比例自动计算"
    }

    private fun calculateRowsForCols(width: Int, height: Int, cols: Int): Int {
        val aspectRatio = height.toFloat() / width.toFloat()
        val calculatedRows = (cols * aspectRatio).toInt()

        // 确保至少有2行，最多8行
        return calculatedRows.coerceIn(2, 8)
    }
    
    private fun checkCameraPermission(): Boolean {
        return if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) 
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, 
                arrayOf(Manifest.permission.CAMERA), 
                REQUEST_CAMERA_PERMISSION)
            false
        } else {
            true
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            REQUEST_CAMERA_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(this, "相机权限已获取", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "需要相机权限才能拍照", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
    
    private fun dispatchTakePictureIntent() {
        Intent(MediaStore.ACTION_IMAGE_CAPTURE).also { takePictureIntent ->
            takePictureIntent.resolveActivity(packageManager)?.also {
                val photoFile: File? = try {
                    createImageFile()
                } catch (ex: IOException) {
                    Toast.makeText(this, "创建图片文件失败", Toast.LENGTH_SHORT).show()
                    null
                }
                
                photoFile?.also {
                    val photoURI: Uri = FileProvider.getUriForFile(
                        this,
                        "com.ai.game_2.fileprovider",
                        it
                    )
                    takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                    startActivityForResult(takePictureIntent, REQUEST_IMAGE_CAPTURE)
                }
            }
        }
    }
    
    @Throws(IOException::class)
    private fun createImageFile(): File {
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir: File? = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "PUZZLE_${timeStamp}_",
            ".jpg",
            storageDir
        ).apply {
            currentPhotoPath = absolutePath
        }
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_IMAGE_CAPTURE && resultCode == RESULT_OK) {
            currentPhotoPath?.let { path ->
                // 处理并保存照片
                val processedPath = processAndSavePhoto(path)
                if (processedPath != null) {
                    photoList.add(processedPath)
                    photoAdapter.notifyItemInserted(photoList.size - 1)
                    updateUI()
                    Toast.makeText(this, "照片已保存", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "照片处理失败", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun processAndSavePhoto(originalPath: String): String? {
        return try {
            val bitmap = BitmapFactory.decodeFile(originalPath)
            if (bitmap == null) return null

            // 压缩图片到合适大小
            val scaledBitmap = scaleBitmap(bitmap, 800, 800)

            // 保存处理后的图片
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "puzzle_photo_${photoList.size + 1}_$timeStamp.jpg"
            val file = File(filesDir, fileName)

            FileOutputStream(file).use { out ->
                scaledBitmap.compress(Bitmap.CompressFormat.JPEG, 85, out)
            }

            file.absolutePath
        } catch (e: Exception) {
            null
        }
    }

    private fun scaleBitmap(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height

        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }

        val scaleWidth = maxWidth.toFloat() / width
        val scaleHeight = maxHeight.toFloat() / height
        val scale = minOf(scaleWidth, scaleHeight)

        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()

        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    private fun removePhoto(position: Int) {
        if (position < photoList.size) {
            // 删除文件
            val filePath = photoList[position]
            val file = File(filePath)
            if (file.exists()) {
                file.delete()
            }

            // 从列表中移除
            photoList.removeAt(position)
            photoAdapter.notifyItemRemoved(position)
            updateUI()
        }
    }

    private fun updateUI() {
        tvPhotoCount.text = "已拍摄 ${photoList.size} 张照片"
        btnStartPuzzle.isEnabled = photoList.isNotEmpty()
        btnStartPuzzle.text = if (photoList.isEmpty()) {
            "请先拍照"
        } else {
            "开始拼图 (${photoList.size}关)"
        }
        updateGridInfo()
    }

    private fun startPuzzleGame() {
        if (photoList.isEmpty()) {
            Toast.makeText(this, "请先拍照", Toast.LENGTH_SHORT).show()
            return
        }

        // 跳转到关卡选择界面，传递照片列表
        val intent = Intent(this, LevelSelectActivity::class.java)
        intent.putExtra("theme", GameTheme.CUSTOM_PHOTO.name)
        intent.putStringArrayListExtra("photo_paths", ArrayList(photoList))
        intent.putExtra("custom_grid_cols", selectedGridCols)
        startActivity(intent)
    }
}
