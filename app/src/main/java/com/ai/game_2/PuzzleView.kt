package com.ai.game_2

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import kotlin.math.min
import kotlin.random.Random

class PuzzleView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var puzzlePieces: MutableList<PuzzlePiece> = mutableListOf()
    var gridSize: Int = 3  // 用于正方形网格
    var gridRows: Int = 3  // 行数 - 公开访问
    var gridCols: Int = 3  // 列数 - 公开访问
    private var pieceWidth: Float = 0f
    private var pieceHeight: Float = 0f
    private var puzzleWidth: Float = 0f
    private var puzzleHeight: Float = 0f
    private var offsetX: Float = 0f
    private var offsetY: Float = 0f
    
    private var selectedPiece: PuzzlePiece? = null
    private var dragOffsetX: Float = 0f
    private var dragOffsetY: Float = 0f
    private var isDragging: Boolean = false
    
    private val paint = Paint().apply {
        isAntiAlias = true
    }
    
    private val borderPaint = Paint().apply {
        color = Color.BLACK
        style = Paint.Style.STROKE
        strokeWidth = 2f
    }

    private val selectedPaint = Paint().apply {
        color = Color.YELLOW
        style = Paint.Style.STROKE
        strokeWidth = 4f
    }

    private val correctPositionPaint = Paint().apply {
        color = Color.GREEN
        style = Paint.Style.STROKE
        strokeWidth = 3f
        alpha = 150
    }

    private val shadowPaint = Paint().apply {
        color = Color.BLACK
        alpha = 50
        maskFilter = BlurMaskFilter(4f, BlurMaskFilter.Blur.NORMAL)
    }
    
    var onPuzzleCompleted: (() -> Unit)? = null
    
    fun initializePuzzle(imageResourceId: Int, gridSize: Int) {
        // 对于非拍照主题，强制使用3x3网格
        this.gridSize = 3
        this.gridRows = 3
        this.gridCols = 3
        val bitmap = getBitmapFromResource(imageResourceId)
        createPuzzlePieces(bitmap)
        shufflePieces()
        invalidate()
    }

    fun initializePuzzleWithCustomPhoto(photoPath: String) {
        val bitmap = getBitmapFromFile(photoPath)
        val gridDimensions = calculateOptimalGrid(bitmap.width, bitmap.height)
        this.gridRows = gridDimensions.first
        this.gridCols = gridDimensions.second
        createPuzzlePiecesWithCustomGrid(bitmap)
        shufflePiecesCustomGrid()
        calculateDimensionsCustomGrid()
        updatePieceBoundsCustomGrid()
        invalidate()
    }

    fun initializePuzzleWithCustomPhotoAndGrid(photoPath: String, cols: Int, rows: Int) {
        val bitmap = getBitmapFromFile(photoPath)
        this.gridRows = rows
        this.gridCols = cols
        createPuzzlePiecesWithCustomGrid(bitmap)
        shufflePiecesCustomGrid()
        calculateDimensionsCustomGrid()
        updatePieceBoundsCustomGrid()
        invalidate()
    }

    fun initializePuzzleWithUrl(imageUrl: String, onComplete: (() -> Unit)? = null) {
        // 检查是否是默认图片
        if (imageUrl.startsWith("default_image_")) {
            // 默认图片使用3x3网格
            this.gridSize = 3
            this.gridRows = 3
            this.gridCols = 3
            useDefaultImage(onComplete)
            return
        }

        try {
            // 使用Glide从URL加载真实图片
            Glide.with(context)
                .asBitmap()
                .load(imageUrl)
                .diskCacheStrategy(DiskCacheStrategy.DATA)
                .timeout(15000) // 15秒超时
                .into(object : CustomTarget<Bitmap>() {
                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        // 根据图片比例动态计算网格
                        calculateDynamicGrid(resource.width, resource.height)

                        // 根据网格类型选择合适的创建方法
                        if (gridRows == gridCols) {
                            // 正方形网格使用原方法
                            createPuzzlePieces(resource)
                        } else {
                            // 自定义网格使用专门的方法
                            createPuzzlePiecesWithCustomGrid(resource)
                        }

                        shufflePieces()

                        // 根据网格类型计算尺寸和边界
                        if (gridRows == gridCols) {
                            calculateDimensions()
                            updatePieceBounds()
                        } else {
                            calculateDimensionsCustomGrid()
                            updatePieceBoundsCustomGrid()
                        }

                        invalidate()
                        onComplete?.invoke()
                    }

                    override fun onLoadCleared(placeholder: android.graphics.drawable.Drawable?) {
                        // 清理资源
                    }

                    override fun onLoadFailed(errorDrawable: android.graphics.drawable.Drawable?) {
                        // 加载失败时使用默认图片
                        useDefaultImage(onComplete)
                    }
                })
        } catch (e: Exception) {
            // 如果Glide初始化失败，直接使用默认图片
            useDefaultImage(onComplete)
        }
    }

    private fun calculateDynamicGrid(imageWidth: Int, imageHeight: Int) {
        // 计算图片的宽高比
        val aspectRatio = imageWidth.toFloat() / imageHeight.toFloat()

        println("图片尺寸: ${imageWidth}x${imageHeight}, 宽高比: $aspectRatio")

        // 根据宽高比动态计算网格
        when {
            aspectRatio > 1.5f -> {
                // 横向图片 (宽 > 高 * 1.5)
                gridCols = 4
                gridRows = 3
                gridSize = 12
                println("横向图片: ${gridCols}x${gridRows} = $gridSize 块")
            }
            aspectRatio < 0.67f -> {
                // 纵向图片 (高 > 宽 * 1.5)
                gridCols = 3
                gridRows = 4
                gridSize = 12
                println("纵向图片: ${gridCols}x${gridRows} = $gridSize 块")
            }
            aspectRatio > 1.2f -> {
                // 稍微横向的图片
                gridCols = 4
                gridRows = 3
                gridSize = 12
                println("稍横向图片: ${gridCols}x${gridRows} = $gridSize 块")
            }
            aspectRatio < 0.83f -> {
                // 稍微纵向的图片
                gridCols = 3
                gridRows = 4
                gridSize = 12
                println("稍纵向图片: ${gridCols}x${gridRows} = $gridSize 块")
            }
            else -> {
                // 接近正方形的图片
                gridCols = 3
                gridRows = 3
                gridSize = 9
                println("正方形图片: ${gridCols}x${gridRows} = $gridSize 块")
            }
        }
    }

    fun initializePuzzleWithGeneratedImage(level: Int, onComplete: (() -> Unit)? = null) {
        // 可爱主题使用3x3网格
        this.gridSize = 3
        this.gridRows = 3
        this.gridCols = 3

        try {
            // 使用CuteImageGenerator生成可爱的卡通角色
            val generator = CuteImageGenerator(context)
            val generatedBitmap = generator.generateCuteImageSync(level)

            if (generatedBitmap != null) {
                createPuzzlePieces(generatedBitmap)
                shufflePieces()
                calculateDimensions()
                updatePieceBounds()
                invalidate()
                onComplete?.invoke()
            } else {
                // 如果关卡不可用或下载失败，使用默认图片
                useDefaultImage(onComplete)
            }
        } catch (e: Exception) {
            // 如果生成失败，使用默认图片
            useDefaultImage(onComplete)
        }
    }

    private fun useDefaultImage(onComplete: (() -> Unit)?) {
        // 默认图片使用3x3网格
        this.gridSize = 3
        this.gridRows = 3
        this.gridCols = 3

        val defaultBitmap = createDefaultBitmap()
        createPuzzlePieces(defaultBitmap)
        shufflePieces()
        invalidate()
        onComplete?.invoke()
    }



    private fun getBitmapFromResource(resourceId: Int): Bitmap {
        val drawable = ContextCompat.getDrawable(context, resourceId)
        return if (drawable != null) {
            // 为矢量图设置合适的大小
            val size = 600 // 设置一个合适的大小
            drawable.setBounds(0, 0, size, size)
            drawable.toBitmap(size, size, Bitmap.Config.ARGB_8888)
        } else {
            // 如果无法获取drawable，创建一个默认的bitmap
            createDefaultBitmap()
        }
    }

    private fun createDefaultBitmap(): Bitmap {
        val size = 600
        val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)

        // 创建一个具有连续性的渐变图案
        val paint = Paint().apply {
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 绘制渐变背景
        val gradient = LinearGradient(
            0f, 0f, size.toFloat(), size.toFloat(),
            intArrayOf(
                Color.parseColor("#FF6B6B"),
                Color.parseColor("#4ECDC4"),
                Color.parseColor("#45B7D1"),
                Color.parseColor("#96CEB4")
            ),
            null,
            Shader.TileMode.CLAMP
        )
        paint.shader = gradient
        canvas.drawRect(0f, 0f, size.toFloat(), size.toFloat(), paint)

        // 重置shader
        paint.shader = null

        // 绘制连续的圆形图案
        paint.color = Color.WHITE
        paint.alpha = 150

        val centerX = size / 2f
        val centerY = size / 2f
        val maxRadius = size / 3f

        // 绘制同心圆
        for (i in 1..5) {
            val radius = maxRadius * i / 5f
            paint.strokeWidth = 8f
            paint.style = Paint.Style.STROKE
            canvas.drawCircle(centerX, centerY, radius, paint)
        }

        // 绘制交叉的波浪线
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 6f
        paint.color = Color.YELLOW
        paint.alpha = 120

        val path = Path()
        for (x in 0..size step 20) {
            path.reset()
            path.moveTo(x.toFloat(), 0f)
            for (y in 0..size step 40) {
                path.quadTo(
                    x + 20f, y + 20f,
                    x.toFloat(), y + 40f
                )
            }
            canvas.drawPath(path, paint)
        }

        return bitmap
    }

    private fun calculateOptimalGrid(width: Int, height: Int): Pair<Int, Int> {
        val aspectRatio = width.toFloat() / height.toFloat()

        return when {
            // 接近正方形 (0.8 - 1.25)
            aspectRatio >= 0.8f && aspectRatio <= 1.25f -> Pair(3, 3)

            // 横向长方形
            aspectRatio > 1.25f && aspectRatio <= 1.6f -> Pair(3, 4)  // 3行4列
            aspectRatio > 1.6f && aspectRatio <= 2.0f -> Pair(3, 5)   // 3行5列
            aspectRatio > 2.0f -> Pair(2, 5)                          // 2行5列

            // 纵向长方形
            aspectRatio < 0.8f && aspectRatio >= 0.625f -> Pair(4, 3) // 4行3列
            aspectRatio < 0.625f && aspectRatio >= 0.5f -> Pair(5, 3) // 5行3列
            aspectRatio < 0.5f -> Pair(5, 2)                          // 5行2列

            else -> Pair(3, 3) // 默认正方形
        }
    }

    private fun getBitmapFromFile(filePath: String): Bitmap {
        return try {
            val bitmap = BitmapFactory.decodeFile(filePath)
            if (bitmap != null) {
                // 确保bitmap有合适的大小
                val targetSize = 600
                Bitmap.createScaledBitmap(bitmap, targetSize, targetSize, true)
            } else {
                createDefaultBitmap()
            }
        } catch (e: Exception) {
            createDefaultBitmap()
        }
    }
    
    private fun createPuzzlePieces(originalBitmap: Bitmap) {
        puzzlePieces.clear()

        // 确保bitmap有合适的大小
        val targetSize = 600
        val scaledBitmap = Bitmap.createScaledBitmap(
            originalBitmap,
            targetSize,
            targetSize,
            true
        )

        val pieceWidth = scaledBitmap.width / gridSize
        val pieceHeight = scaledBitmap.height / gridSize

        // 重叠边距，让相邻拼图块有视觉连续性
        val overlapMargin = (pieceWidth * 0.1f).toInt() // 10%的重叠

        var id = 0
        for (row in 0 until gridSize) {
            for (col in 0 until gridSize) {
                // 计算扩展的切割区域，包含重叠部分
                val x = (col * pieceWidth - if (col > 0) overlapMargin else 0).coerceAtLeast(0)
                val y = (row * pieceHeight - if (row > 0) overlapMargin else 0).coerceAtLeast(0)

                val width = (pieceWidth +
                    (if (col > 0) overlapMargin else 0) +
                    (if (col < gridSize - 1) overlapMargin else 0))
                    .coerceAtMost(scaledBitmap.width - x)

                val height = (pieceHeight +
                    (if (row > 0) overlapMargin else 0) +
                    (if (row < gridSize - 1) overlapMargin else 0))
                    .coerceAtMost(scaledBitmap.height - y)

                // 创建带有重叠内容的拼图块
                val pieceBitmap = createEnhancedPuzzlePiece(
                    scaledBitmap, x, y, width, height,
                    row, col, pieceWidth, pieceHeight, overlapMargin
                )

                val piece = PuzzlePiece(
                    id = id++,
                    correctRow = row,
                    correctCol = col,
                    currentRow = row,
                    currentCol = col,
                    bitmap = pieceBitmap
                )

                puzzlePieces.add(piece)
            }
        }
    }

    private fun createEnhancedPuzzlePiece(
        sourceBitmap: Bitmap,
        sourceX: Int,
        sourceY: Int,
        sourceWidth: Int,
        sourceHeight: Int,
        row: Int,
        col: Int,
        standardWidth: Int,
        standardHeight: Int,
        overlapMargin: Int
    ): Bitmap {
        // 创建标准大小的拼图块bitmap
        val pieceBitmap = Bitmap.createBitmap(standardWidth, standardHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(pieceBitmap)

        // 计算源图像在拼图块中的位置
        val destX = if (col > 0) -overlapMargin else 0
        val destY = if (row > 0) -overlapMargin else 0

        // 绘制主要内容
        val srcRect = Rect(sourceX, sourceY, sourceX + sourceWidth, sourceY + sourceHeight)
        val destRect = Rect(destX, destY, destX + sourceWidth, destY + sourceHeight)
        canvas.drawBitmap(sourceBitmap, srcRect, destRect, paint)

        // 添加边缘连接提示
        addConnectionHints(canvas, sourceBitmap, row, col, standardWidth, standardHeight, overlapMargin)

        return pieceBitmap
    }

    private fun addConnectionHints(
        canvas: Canvas,
        sourceBitmap: Bitmap,
        row: Int,
        col: Int,
        width: Int,
        height: Int,
        margin: Int
    ) {
        val hintPaint = Paint().apply {
            alpha = 100 // 半透明提示
            isAntiAlias = true
        }

        val currentGridCols = if (gridRows == gridCols) gridSize else gridCols
        val currentGridRows = if (gridRows == gridCols) gridSize else gridRows
        val pieceWidth = sourceBitmap.width / currentGridCols
        val pieceHeight = sourceBitmap.height / currentGridRows
        val hintSize = margin / 2

        // 右边连接提示
        if (col < currentGridCols - 1) {
            val rightX = (col + 1) * pieceWidth
            if (rightX < sourceBitmap.width) {
                val srcRect = Rect(rightX, row * pieceHeight,
                    (rightX + hintSize).coerceAtMost(sourceBitmap.width),
                    ((row + 1) * pieceHeight).coerceAtMost(sourceBitmap.height))
                val destRect = Rect(width - hintSize, margin, width, height - margin)
                canvas.drawBitmap(sourceBitmap, srcRect, destRect, hintPaint)
            }
        }

        // 下边连接提示
        if (row < currentGridRows - 1) {
            val bottomY = (row + 1) * pieceHeight
            if (bottomY < sourceBitmap.height) {
                val srcRect = Rect(col * pieceWidth, bottomY,
                    ((col + 1) * pieceWidth).coerceAtMost(sourceBitmap.width),
                    (bottomY + hintSize).coerceAtMost(sourceBitmap.height))
                val destRect = Rect(margin, height - hintSize, width - margin, height)
                canvas.drawBitmap(sourceBitmap, srcRect, destRect, hintPaint)
            }
        }

        // 左边连接提示
        if (col > 0) {
            val leftX = (col - 1) * pieceWidth + pieceWidth - hintSize
            if (leftX >= 0) {
                val srcRect = Rect(leftX, row * pieceHeight,
                    col * pieceWidth,
                    ((row + 1) * pieceHeight).coerceAtMost(sourceBitmap.height))
                val destRect = Rect(0, margin, hintSize, height - margin)
                canvas.drawBitmap(sourceBitmap, srcRect, destRect, hintPaint)
            }
        }

        // 上边连接提示
        if (row > 0) {
            val topY = (row - 1) * pieceHeight + pieceHeight - hintSize
            if (topY >= 0) {
                val srcRect = Rect(col * pieceWidth, topY,
                    ((col + 1) * pieceWidth).coerceAtMost(sourceBitmap.width),
                    row * pieceHeight)
                val destRect = Rect(margin, 0, width - margin, hintSize)
                canvas.drawBitmap(sourceBitmap, srcRect, destRect, hintPaint)
            }
        }
    }
    
    private fun shufflePieces() {
        // 对于正方形网格，使用gridSize
        if (gridRows == gridCols) {
            shufflePiecesSquareGrid()
        } else {
            shufflePiecesCustomGrid()
        }
    }

    private fun shufflePiecesSquareGrid() {
        // 确保拼图块数量正确
        val expectedPieceCount = gridSize * gridSize
        if (puzzlePieces.size != expectedPieceCount) {
            Log.e("PuzzleView", "正方形拼图块数量不匹配: 期望 $expectedPieceCount, 实际 ${puzzlePieces.size}")
            return
        }

        // 创建一个位置列表并打乱
        val positions = mutableListOf<Pair<Int, Int>>()
        for (row in 0 until gridSize) {
            for (col in 0 until gridSize) {
                positions.add(Pair(row, col))
            }
        }
        positions.shuffle()

        // 将打乱的位置分配给拼图块
        puzzlePieces.forEachIndexed { index, piece ->
            if (index < positions.size) {
                val (row, col) = positions[index]
                piece.currentRow = row
                piece.currentCol = col
            } else {
                Log.e("PuzzleView", "正方形拼图索引越界: index=$index, positions.size=${positions.size}")
            }
        }

        // 确保拼图不是已完成状态
        ensurePuzzleIsNotCompleted()
    }

    private fun ensurePuzzleIsNotCompleted() {
        // 如果拼图已经完成，交换前两个拼图块
        if (isPuzzleCompleted() && puzzlePieces.size >= 2) {
            puzzlePieces[0].swapPosition(puzzlePieces[1])
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (gridRows == gridCols) {
            calculateDimensions()
            updatePieceBounds()
        } else {
            calculateDimensionsCustomGrid()
            updatePieceBoundsCustomGrid()
        }
    }
    
    private fun calculateDimensions() {
        val availableSize = min(width, height) * 0.9f
        puzzleWidth = availableSize
        puzzleHeight = availableSize
        pieceWidth = puzzleWidth / gridSize
        pieceHeight = puzzleHeight / gridSize
        offsetX = (width - puzzleWidth) / 2f
        offsetY = (height - puzzleHeight) / 2f
    }
    
    private fun updatePieceBounds() {
        puzzlePieces.forEach { piece ->
            val left = offsetX + piece.currentCol * pieceWidth
            val top = offsetY + piece.currentRow * pieceHeight
            val right = left + pieceWidth
            val bottom = top + pieceHeight
            piece.updateBounds(left, top, right, bottom)
        }
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        // 绘制拼图块
        puzzlePieces.forEach { piece ->
            if (piece != selectedPiece || !isDragging) {
                drawPiece(canvas, piece)
            }
        }
        
        // 最后绘制被选中的拼图块（在最上层）
        selectedPiece?.let { piece ->
            if (isDragging) {
                drawPiece(canvas, piece)
            }
        }
    }
    
    private fun drawPiece(canvas: Canvas, piece: PuzzlePiece) {
        val destRect = RectF(piece.bounds)

        // 如果正在拖拽，调整位置
        if (piece == selectedPiece && isDragging) {
            destRect.offset(dragOffsetX, dragOffsetY)

            // 绘制阴影效果
            val shadowRect = RectF(destRect)
            shadowRect.offset(4f, 4f)
            canvas.drawRect(shadowRect, shadowPaint)
        }

        // 绘制拼图块
        canvas.drawBitmap(piece.bitmap, null, destRect, paint)

        // 绘制边框
        val borderColor = when {
            piece == selectedPiece -> selectedPaint
            piece.isInCorrectPosition() -> correctPositionPaint
            else -> borderPaint
        }
        canvas.drawRect(destRect, borderColor)

        // 如果拼图块在正确位置，添加连接指示器
        if (piece.isInCorrectPosition()) {
            drawConnectionIndicators(canvas, piece, destRect)
        }
    }

    private fun drawConnectionIndicators(canvas: Canvas, piece: PuzzlePiece, rect: RectF) {
        val indicatorPaint = Paint().apply {
            color = Color.GREEN
            alpha = 100
            style = Paint.Style.FILL
        }

        val indicatorSize = 8f
        val margin = 4f

        // 检查相邻拼图块是否也在正确位置
        val correctNeighbors = getCorrectNeighbors(piece)

        // 右边连接指示器
        if (correctNeighbors.contains("right")) {
            canvas.drawCircle(
                rect.right - margin,
                rect.centerY(),
                indicatorSize,
                indicatorPaint
            )
        }

        // 下边连接指示器
        if (correctNeighbors.contains("bottom")) {
            canvas.drawCircle(
                rect.centerX(),
                rect.bottom - margin,
                indicatorSize,
                indicatorPaint
            )
        }

        // 左边连接指示器
        if (correctNeighbors.contains("left")) {
            canvas.drawCircle(
                rect.left + margin,
                rect.centerY(),
                indicatorSize,
                indicatorPaint
            )
        }

        // 上边连接指示器
        if (correctNeighbors.contains("top")) {
            canvas.drawCircle(
                rect.centerX(),
                rect.top + margin,
                indicatorSize,
                indicatorPaint
            )
        }
    }

    private fun getCorrectNeighbors(piece: PuzzlePiece): Set<String> {
        val correctNeighbors = mutableSetOf<String>()

        puzzlePieces.forEach { otherPiece ->
            if (otherPiece != piece && otherPiece.isInCorrectPosition()) {
                // 检查是否是右邻居
                if (otherPiece.correctRow == piece.correctRow &&
                    otherPiece.correctCol == piece.correctCol + 1) {
                    correctNeighbors.add("right")
                }
                // 检查是否是下邻居
                if (otherPiece.correctRow == piece.correctRow + 1 &&
                    otherPiece.correctCol == piece.correctCol) {
                    correctNeighbors.add("bottom")
                }
                // 检查是否是左邻居
                if (otherPiece.correctRow == piece.correctRow &&
                    otherPiece.correctCol == piece.correctCol - 1) {
                    correctNeighbors.add("left")
                }
                // 检查是否是上邻居
                if (otherPiece.correctRow == piece.correctRow - 1 &&
                    otherPiece.correctCol == piece.correctCol) {
                    correctNeighbors.add("top")
                }
            }
        }

        return correctNeighbors
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                val touchedPiece = findPieceAt(event.x, event.y)
                if (touchedPiece != null) {
                    selectedPiece = touchedPiece
                    dragOffsetX = 0f
                    dragOffsetY = 0f
                    isDragging = true
                    invalidate()
                    return true
                }
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragging && selectedPiece != null) {
                    dragOffsetX = event.x - selectedPiece!!.bounds.centerX()
                    dragOffsetY = event.y - selectedPiece!!.bounds.centerY()
                    invalidate()
                    return true
                }
            }

            MotionEvent.ACTION_UP -> {
                if (isDragging && selectedPiece != null) {
                    val targetPiece = findPieceAt(event.x, event.y)
                    if (targetPiece != null && targetPiece != selectedPiece) {
                        // 交换两个拼图块的位置
                        selectedPiece!!.swapPosition(targetPiece)
                        if (gridRows == gridCols) {
                            updatePieceBounds()
                        } else {
                            updatePieceBoundsCustomGrid()
                        }

                        // 检查是否完成拼图
                        if (isPuzzleCompleted()) {
                            Log.d("PuzzleView", "拼图完成！触发回调")
                            onPuzzleCompleted?.invoke()
                        }
                    }

                    isDragging = false
                    dragOffsetX = 0f
                    dragOffsetY = 0f
                    selectedPiece = null
                    invalidate()
                    return true
                }
            }
        }
        return super.onTouchEvent(event)
    }

    private fun findPieceAt(x: Float, y: Float): PuzzlePiece? {
        // 从后往前查找，确保选中最上层的拼图块
        for (i in puzzlePieces.size - 1 downTo 0) {
            val piece = puzzlePieces[i]
            if (piece.contains(x, y)) {
                return piece
            }
        }
        return null
    }

    private fun isPuzzleCompleted(): Boolean {
        val completed = puzzlePieces.all { it.isInCorrectPosition() }
        Log.d("PuzzleView", "检查拼图完成状态: $completed")
        if (completed) {
            puzzlePieces.forEach { piece ->
                Log.d("PuzzleView", "拼图块 ${piece.id}: 正确位置(${piece.correctRow},${piece.correctCol}) 当前位置(${piece.currentRow},${piece.currentCol})")
            }
        }
        return completed
    }

    fun resetPuzzle() {
        shufflePieces()
        if (gridRows == gridCols) {
            updatePieceBounds()
        } else {
            updatePieceBoundsCustomGrid()
        }
        selectedPiece = null
        isDragging = false
        invalidate()
    }

    private fun createPuzzlePiecesWithCustomGrid(originalBitmap: Bitmap) {
        puzzlePieces.clear()

        // 确保bitmap有合适的大小，保持原始宽高比
        val maxSize = 800
        val scaledBitmap = if (originalBitmap.width > maxSize || originalBitmap.height > maxSize) {
            val scale = minOf(maxSize.toFloat() / originalBitmap.width, maxSize.toFloat() / originalBitmap.height)
            val newWidth = (originalBitmap.width * scale).toInt()
            val newHeight = (originalBitmap.height * scale).toInt()
            Bitmap.createScaledBitmap(originalBitmap, newWidth, newHeight, true)
        } else {
            originalBitmap
        }

        val pieceWidth = scaledBitmap.width / gridCols
        val pieceHeight = scaledBitmap.height / gridRows

        var id = 0
        for (row in 0 until gridRows) {
            for (col in 0 until gridCols) {
                val x = col * pieceWidth
                val y = row * pieceHeight

                val pieceBitmap = Bitmap.createBitmap(
                    scaledBitmap, x, y, pieceWidth, pieceHeight
                )

                val piece = PuzzlePiece(
                    id = id++,
                    correctRow = row,
                    correctCol = col,
                    currentRow = row,
                    currentCol = col,
                    bitmap = pieceBitmap
                )

                puzzlePieces.add(piece)
            }
        }
    }

    private fun shufflePiecesCustomGrid() {
        // 确保拼图块数量正确
        val expectedPieceCount = gridRows * gridCols
        if (puzzlePieces.size != expectedPieceCount) {
            Log.e("PuzzleView", "拼图块数量不匹配: 期望 $expectedPieceCount, 实际 ${puzzlePieces.size}")
            return
        }

        // 创建一个位置列表并打乱
        val positions = mutableListOf<Pair<Int, Int>>()
        for (row in 0 until gridRows) {
            for (col in 0 until gridCols) {
                positions.add(Pair(row, col))
            }
        }
        positions.shuffle()

        // 将打乱的位置分配给拼图块
        puzzlePieces.forEachIndexed { index, piece ->
            if (index < positions.size) {
                val (row, col) = positions[index]
                piece.currentRow = row
                piece.currentCol = col
            } else {
                Log.e("PuzzleView", "索引越界: index=$index, positions.size=${positions.size}")
            }
        }

        // 确保拼图不是已完成状态
        ensurePuzzleIsNotCompletedCustomGrid()
    }

    private fun ensurePuzzleIsNotCompletedCustomGrid() {
        // 如果拼图已经完成，交换前两个拼图块
        if (isPuzzleCompleted() && puzzlePieces.size >= 2) {
            puzzlePieces[0].swapPosition(puzzlePieces[1])
        }
    }

    private fun calculateDimensionsCustomGrid() {
        val availableWidth = width * 0.9f
        val availableHeight = height * 0.9f

        // 根据网格比例计算拼图尺寸
        val aspectRatio = gridCols.toFloat() / gridRows.toFloat()

        if (availableWidth / availableHeight > aspectRatio) {
            // 高度限制
            puzzleHeight = availableHeight
            puzzleWidth = puzzleHeight * aspectRatio
        } else {
            // 宽度限制
            puzzleWidth = availableWidth
            puzzleHeight = puzzleWidth / aspectRatio
        }

        pieceWidth = puzzleWidth / gridCols
        pieceHeight = puzzleHeight / gridRows
        offsetX = (width - puzzleWidth) / 2f
        offsetY = (height - puzzleHeight) / 2f
    }

    private fun updatePieceBoundsCustomGrid() {
        puzzlePieces.forEach { piece ->
            val left = offsetX + piece.currentCol * pieceWidth
            val top = offsetY + piece.currentRow * pieceHeight
            val right = left + pieceWidth
            val bottom = top + pieceHeight
            piece.updateBounds(left, top, right, bottom)
        }
    }
}
