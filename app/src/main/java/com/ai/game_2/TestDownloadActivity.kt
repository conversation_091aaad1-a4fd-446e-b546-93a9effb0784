package com.ai.game_2

import android.os.Bundle
import android.widget.Button
import android.widget.ScrollView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import kotlinx.coroutines.*

class TestDownloadActivity : AppCompatActivity() {
    
    private lateinit var resultText: TextView
    private lateinit var testButton: Button
    private lateinit var scrollView: ScrollView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 创建简单的布局
        val layout = android.widget.LinearLayout(this)
        layout.orientation = android.widget.LinearLayout.VERTICAL
        layout.setPadding(32, 32, 32, 32)
        
        testButton = Button(this)
        testButton.text = "测试第1关下载"
        testButton.setOnClickListener {
            testDownload()
        }
        
        resultText = TextView(this)
        resultText.text = "点击按钮开始测试..."
        resultText.textSize = 12f
        
        scrollView = ScrollView(this)
        scrollView.addView(resultText)
        
        layout.addView(testButton)
        layout.addView(scrollView)
        
        setContentView(layout)
    }
    
    private fun testDownload() {
        testButton.isEnabled = false
        resultText.text = "开始测试...\n"
        
        CoroutineScope(Dispatchers.Main).launch {
            try {
                val generator = CuteImageGenerator(this@TestDownloadActivity)
                
                // 清理之前的数据
                generator.clearAllData()
                resultText.append("已清理之前的数据\n")
                
                // 测试搜索功能
                val searchResult = generator.testSearch(1)
                resultText.append("搜索测试结果:\n$searchResult\n")
                
                // 测试实际下载
                resultText.append("开始实际下载测试...\n")
                val bitmap = generator.generateCuteImage(1)
                
                if (bitmap != null) {
                    resultText.append("✅ 第1关下载成功！\n")
                    resultText.append("图片尺寸: ${bitmap.width}x${bitmap.height}\n")
                    
                    // 检查本地文件
                    val downloadedLevels = generator.getDownloadedLevels()
                    resultText.append("已下载关卡: $downloadedLevels\n")
                } else {
                    resultText.append("❌ 第1关下载失败\n")
                    
                    // 检查失败记录
                    val failedLevels = generator.getFailedLevels()
                    resultText.append("失败关卡: $failedLevels\n")
                }
                
            } catch (e: Exception) {
                resultText.append("测试异常: ${e.message}\n")
                e.printStackTrace()
            } finally {
                testButton.isEnabled = true
            }
        }
    }
}
