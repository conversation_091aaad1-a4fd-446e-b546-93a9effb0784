package com.ai.game_2

import android.content.Context
import android.graphics.*
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import kotlin.math.cos
import kotlin.math.sin

class CuteImageGenerator(private val context: Context) {

    private val width = 400
    private val height = 400

    // 图片永久存储目录（使用内部存储，不会被清理）
    private val imageDir = File(context.filesDir, "cute_images")

    // 下载失败记录文件
    private val failedDownloadsFile = File(context.filesDir, "failed_downloads.txt")

    // 简笔画搜索关键词
    private val cuteThemes = listOf(
        "cat simple drawing outline",
        "car simple drawing outline",
        "dinosaur simple drawing outline",
        "castle simple drawing outline",
        "rocket simple drawing outline",
        "flower simple drawing outline",
        "robot simple drawing outline",
        "butterfly simple drawing outline",
        "house simple drawing outline",
        "unicorn simple drawing outline"
    )

    init {
        // 创建图片存储目录
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        }
    }

    // 免费图片网站API（无版权问题）
    private val freeImageSources = listOf(
        "https://pixabay.com/api/",
        "https://api.unsplash.com/search/photos",
        "https://api.pexels.com/v1/search"
    )

    // 简笔画图片URL（来自免费简笔画网站，无版权问题）
    private val simpleDrawingUrls = mapOf(
        1 to listOf( // 小猫简笔画
            "https://cdn.pixabay.com/photo/2017/01/31/15/33/animal-2025795_960_720.png",
            "https://cdn.pixabay.com/photo/2016/03/31/19/50/cat-1294821_960_720.png",
            "https://cdn.pixabay.com/photo/2017/06/24/02/56/cat-2436545_960_720.png"
        ),
        2 to listOf( // 汽车简笔画
            "https://cdn.pixabay.com/photo/2013/07/12/15/36/car-150157_960_720.png",
            "https://cdn.pixabay.com/photo/2016/04/01/12/11/vehicle-1300629_960_720.png",
            "https://cdn.pixabay.com/photo/2012/04/18/00/07/car-36343_960_720.png"
        ),
        3 to listOf( // 恐龙简笔画
            "https://cdn.pixabay.com/photo/2017/06/14/16/20/dinosaur-2402841_960_720.png",
            "https://cdn.pixabay.com/photo/2016/04/15/18/05/dinosaur-1331579_960_720.png",
            "https://cdn.pixabay.com/photo/2017/09/30/09/29/dino-2798507_960_720.png"
        ),
        4 to listOf( // 城堡简笔画
            "https://cdn.pixabay.com/photo/2017/08/30/12/45/castle-2696756_960_720.png",
            "https://cdn.pixabay.com/photo/2015/09/05/20/02/castle-926736_960_720.png",
            "https://cdn.pixabay.com/photo/2013/02/21/19/06/building-84686_960_720.png"
        ),
        5 to listOf( // 火箭简笔画
            "https://cdn.pixabay.com/photo/2017/01/31/15/33/rocket-2025795_960_720.png",
            "https://cdn.pixabay.com/photo/2016/09/10/17/18/space-1659442_960_720.png",
            "https://cdn.pixabay.com/photo/2011/12/14/12/21/rocket-11095_960_720.png"
        ),
        6 to listOf( // 花朵简笔画
            "https://cdn.pixabay.com/photo/2015/04/19/08/32/flower-729510_960_720.png",
            "https://cdn.pixabay.com/photo/2013/07/21/13/00/flower-165819_960_720.png",
            "https://cdn.pixabay.com/photo/2016/11/29/05/45/flower-1867616_960_720.png"
        ),
        7 to listOf( // 机器人简笔画
            "https://cdn.pixabay.com/photo/2017/01/25/12/31/robot-2007707_960_720.png",
            "https://cdn.pixabay.com/photo/2017/05/26/19/36/android-2345195_960_720.png",
            "https://cdn.pixabay.com/photo/2016/12/09/09/52/robot-1894125_960_720.png"
        ),
        8 to listOf( // 蝴蝶简笔画
            "https://cdn.pixabay.com/photo/2016/03/26/22/13/butterfly-1281980_960_720.png",
            "https://cdn.pixabay.com/photo/2015/08/13/20/06/insect-887079_960_720.png",
            "https://cdn.pixabay.com/photo/2017/05/11/19/44/butterfly-2305192_960_720.png"
        ),
        9 to listOf( // 房子简笔画
            "https://cdn.pixabay.com/photo/2016/11/18/17/20/home-1836070_960_720.png",
            "https://cdn.pixabay.com/photo/2017/03/22/17/39/building-2167734_960_720.png",
            "https://cdn.pixabay.com/photo/2014/07/10/17/18/house-389271_960_720.png"
        ),
        10 to listOf( // 独角兽简笔画
            "https://cdn.pixabay.com/photo/2017/01/16/15/17/unicorn-1984690_960_720.png",
            "https://cdn.pixabay.com/photo/2016/03/31/19/50/unicorn-1294821_960_720.png",
            "https://cdn.pixabay.com/photo/2017/06/24/02/56/fantasy-2436545_960_720.png"
        )
    )
    
    // 检查关卡是否可用（下载是否成功）
    fun isLevelAvailable(level: Int): Boolean {
        // 检查是否有本地图片
        if (hasLocalImage(level)) {
            return true
        }

        // 检查是否在失败列表中
        return !isLevelFailed(level)
    }

    // 异步生成可爱图片
    suspend fun generateCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 检查关卡是否可用
                if (!isLevelAvailable(level)) {
                    return@withContext null // 关卡不可用，返回null
                }

                // 尝试从本地或网络获取图片
                downloadCuteImage(level)
            } catch (e: Exception) {
                // 下载失败，标记关卡为不可用
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 同步版本（保持兼容性）
    fun generateCuteImageSync(level: Int): Bitmap? {
        return runBlocking {
            generateCuteImage(level)
        }
    }
    
    // 从本地或网络获取简笔画图片
    private suspend fun downloadCuteImage(level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                // 首先检查本地是否已有图片
                val localBitmap = getLocalImage(level)
                if (localBitmap != null) {
                    return@withContext localBitmap // 直接返回本地图片，不重复下载
                }

                // 本地没有，从网络下载简笔画
                val drawingUrls = simpleDrawingUrls[level]
                if (drawingUrls != null) {
                    for (url in drawingUrls) {
                        try {
                            val bitmap = downloadAndSaveImage(url, level)
                            if (bitmap != null) {
                                return@withContext resizeBitmap(bitmap, width, height)
                            }
                        } catch (e: Exception) {
                            continue
                        }
                    }
                }

                // 如果预设URL失败，尝试搜索简笔画
                val keyword = cuteThemes.getOrNull(level - 1) ?: cuteThemes[0]
                val imageUrls = searchSimpleDrawings(keyword)

                // 尝试下载搜索到的简笔画
                for (imageUrl in imageUrls.take(2)) { // 最多尝试2个
                    try {
                        val bitmap = downloadAndSaveImage(imageUrl, level)
                        if (bitmap != null) {
                            return@withContext resizeBitmap(bitmap, width, height)
                        }
                    } catch (e: Exception) {
                        continue
                    }
                }

                // 所有下载都失败，标记为失败
                markLevelAsFailed(level)
                null
            } catch (e: Exception) {
                markLevelAsFailed(level)
                null
            }
        }
    }

    // 检查本地是否有图片
    private fun hasLocalImage(level: Int): Boolean {
        val imageFile = File(imageDir, "cute_level_$level.png")
        return imageFile.exists()
    }

    // 获取本地图片
    private fun getLocalImage(level: Int): Bitmap? {
        return try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            if (imageFile.exists()) {
                BitmapFactory.decodeFile(imageFile.absolutePath)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    // 下载图片并永久保存
    private suspend fun downloadAndSaveImage(imageUrl: String, level: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val bitmap = downloadImageFromUrl(imageUrl)
                if (bitmap != null) {
                    // 永久保存到本地
                    saveBitmapToLocal(bitmap, level)
                    bitmap
                } else {
                    null
                }
            } catch (e: Exception) {
                null
            }
        }
    }

    // 永久保存图片到本地
    private fun saveBitmapToLocal(bitmap: Bitmap, level: Int) {
        try {
            val imageFile = File(imageDir, "cute_level_$level.png")
            val outputStream = FileOutputStream(imageFile)
            bitmap.compress(Bitmap.CompressFormat.PNG, 95, outputStream) // 高质量保存
            outputStream.close()
        } catch (e: Exception) {
            // 保存失败不影响主要功能
        }
    }

    // 检查关卡是否下载失败
    private fun isLevelFailed(level: Int): Boolean {
        return try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",")
                failedLevels.contains(level.toString())
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }

    // 标记关卡为下载失败
    private fun markLevelAsFailed(level: Int) {
        try {
            val failedLevels = if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText().split(",").toMutableSet()
            } else {
                mutableSetOf()
            }

            failedLevels.add(level.toString())
            failedDownloadsFile.writeText(failedLevels.joinToString(","))
        } catch (e: Exception) {
            // 记录失败不影响主要功能
        }
    }

    // 清理所有本地图片和失败记录（重置功能）
    fun clearAllData() {
        try {
            // 删除所有本地图片
            if (imageDir.exists()) {
                imageDir.listFiles()?.forEach { file ->
                    if (file.name.startsWith("cute_level_")) {
                        file.delete()
                    }
                }
            }

            // 删除失败记录
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.delete()
            }
        } catch (e: Exception) {
            // 清理失败不影响主要功能
        }
    }

    // 重新尝试失败的关卡
    fun retryFailedLevel(level: Int) {
        try {
            if (failedDownloadsFile.exists()) {
                val failedLevels = failedDownloadsFile.readText().split(",").toMutableSet()
                failedLevels.remove(level.toString())

                if (failedLevels.isEmpty()) {
                    failedDownloadsFile.delete()
                } else {
                    failedDownloadsFile.writeText(failedLevels.joinToString(","))
                }
            }
        } catch (e: Exception) {
            // 重试失败不影响主要功能
        }
    }

    // 检查本地存储大小
    fun getLocalStorageSize(): Long {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()?.sumOf { it.length() } ?: 0L
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }

    // 获取已下载的关卡列表
    fun getDownloadedLevels(): List<Int> {
        return try {
            if (imageDir.exists()) {
                imageDir.listFiles()
                    ?.filter { it.name.startsWith("cute_level_") && it.name.endsWith(".png") }
                    ?.mapNotNull { file ->
                        val levelStr = file.name.removePrefix("cute_level_").removeSuffix(".png")
                        levelStr.toIntOrNull()
                    }
                    ?.sorted() ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    // 获取失败的关卡列表
    fun getFailedLevels(): List<Int> {
        return try {
            if (failedDownloadsFile.exists()) {
                failedDownloadsFile.readText()
                    .split(",")
                    .mapNotNull { it.toIntOrNull() }
                    .sorted()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    // 搜索简笔画图片
    private suspend fun searchSimpleDrawings(keyword: String): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                val imageUrls = mutableListOf<String>()

                // 尝试搜索简笔画
                imageUrls.addAll(searchDrawingImages(keyword))

                // 如果搜索没有结果，使用默认简笔画
                if (imageUrls.isEmpty()) {
                    imageUrls.addAll(getDefaultSimpleDrawings(keyword))
                }

                imageUrls
            } catch (e: Exception) {
                getDefaultSimpleDrawings(keyword)
            }
        }
    }

    // 搜索简笔画图片（专门针对简笔画）
    private suspend fun searchDrawingImages(keyword: String): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                // 这里可以调用专门的简笔画API或搜索引擎
                // 目前使用预设的简笔画URL
                getDefaultSimpleDrawings(keyword)
            } catch (e: Exception) {
                emptyList()
            }
        }
    }

    // 获取默认的简笔画URL
    private fun getDefaultSimpleDrawings(keyword: String): List<String> {
        return when {
            keyword.contains("cat") -> listOf(
                "https://cdn.pixabay.com/photo/2017/01/31/15/33/animal-2025795_960_720.png",
                "https://cdn.pixabay.com/photo/2016/03/31/19/50/cat-1294821_960_720.png",
                "https://cdn.pixabay.com/photo/2017/06/24/02/56/cat-2436545_960_720.png"
            )
            keyword.contains("car") -> listOf(
                "https://cdn.pixabay.com/photo/2013/07/12/15/36/car-150157_960_720.png",
                "https://cdn.pixabay.com/photo/2016/04/01/12/11/vehicle-1300629_960_720.png",
                "https://cdn.pixabay.com/photo/2012/04/18/00/07/car-36343_960_720.png"
            )
            keyword.contains("dinosaur") -> listOf(
                "https://cdn.pixabay.com/photo/2017/06/14/16/20/dinosaur-2402841_960_720.png",
                "https://cdn.pixabay.com/photo/2016/04/15/18/05/dinosaur-1331579_960_720.png",
                "https://cdn.pixabay.com/photo/2017/09/30/09/29/dino-2798507_960_720.png"
            )
            keyword.contains("castle") -> listOf(
                "https://cdn.pixabay.com/photo/2017/08/30/12/45/castle-2696756_960_720.png",
                "https://cdn.pixabay.com/photo/2015/09/05/20/02/castle-926736_960_720.png",
                "https://cdn.pixabay.com/photo/2013/02/21/19/06/building-84686_960_720.png"
            )
            keyword.contains("rocket") -> listOf(
                "https://cdn.pixabay.com/photo/2017/01/31/15/33/rocket-2025795_960_720.png",
                "https://cdn.pixabay.com/photo/2016/09/10/17/18/space-1659442_960_720.png",
                "https://cdn.pixabay.com/photo/2011/12/14/12/21/rocket-11095_960_720.png"
            )
            keyword.contains("flower") -> listOf(
                "https://cdn.pixabay.com/photo/2015/04/19/08/32/flower-729510_960_720.png",
                "https://cdn.pixabay.com/photo/2013/07/21/13/00/flower-165819_960_720.png",
                "https://cdn.pixabay.com/photo/2016/11/29/05/45/flower-1867616_960_720.png"
            )
            keyword.contains("robot") -> listOf(
                "https://cdn.pixabay.com/photo/2017/01/25/12/31/robot-2007707_960_720.png",
                "https://cdn.pixabay.com/photo/2017/05/26/19/36/android-2345195_960_720.png",
                "https://cdn.pixabay.com/photo/2016/12/09/09/52/robot-1894125_960_720.png"
            )
            keyword.contains("butterfly") -> listOf(
                "https://cdn.pixabay.com/photo/2016/03/26/22/13/butterfly-1281980_960_720.png",
                "https://cdn.pixabay.com/photo/2015/08/13/20/06/insect-887079_960_720.png",
                "https://cdn.pixabay.com/photo/2017/05/11/19/44/butterfly-2305192_960_720.png"
            )
            keyword.contains("house") -> listOf(
                "https://cdn.pixabay.com/photo/2016/11/18/17/20/home-1836070_960_720.png",
                "https://cdn.pixabay.com/photo/2017/03/22/17/39/building-2167734_960_720.png",
                "https://cdn.pixabay.com/photo/2014/07/10/17/18/house-389271_960_720.png"
            )
            keyword.contains("unicorn") -> listOf(
                "https://cdn.pixabay.com/photo/2017/01/16/15/17/unicorn-1984690_960_720.png",
                "https://cdn.pixabay.com/photo/2016/03/31/19/50/unicorn-1294821_960_720.png",
                "https://cdn.pixabay.com/photo/2017/06/24/02/56/fantasy-2436545_960_720.png"
            )
            else -> listOf(
                "https://cdn.pixabay.com/photo/2017/01/31/15/33/animal-2025795_960_720.png",
                "https://cdn.pixabay.com/photo/2013/07/12/15/36/car-150157_960_720.png",
                "https://cdn.pixabay.com/photo/2017/08/30/12/45/castle-2696756_960_720.png"
            )
        }
    }
    
    // 从URL下载图片
    private suspend fun downloadImageFromUrl(imageUrl: String): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val url = URL(imageUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.connectTimeout = 5000
                connection.readTimeout = 10000
                connection.doInput = true
                connection.connect()
                
                val inputStream: InputStream = connection.inputStream
                val bitmap = BitmapFactory.decodeStream(inputStream)
                inputStream.close()
                connection.disconnect()
                
                bitmap
            } catch (e: Exception) {
                null
            }
        }
    }
    
    // 调整图片大小
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        return Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
    }
}
