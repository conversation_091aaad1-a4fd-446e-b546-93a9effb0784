<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="200dp"
    android:layout_margin="16dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="20dp"
        android:gravity="center_vertical">

        <!-- 主题图片 -->
        <ImageView
            android:id="@+id/iv_theme_image"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:scaleType="centerCrop"
            android:background="@drawable/image_border"
            android:layout_marginEnd="20dp" />

        <!-- 主题信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_theme_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="海绵宝宝 🧽"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/primary_text_color"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tv_theme_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="和海绵宝宝一起探索比奇堡的海底世界！"
                android:textSize="16sp"
                android:textColor="@color/secondary_text_color"
                android:layout_marginBottom="12dp"
                android:lineSpacingExtra="4dp" />

            <TextView
                android:id="@+id/tv_level_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10个关卡"
                android:textSize="14sp"
                android:textColor="@color/accent_color"
                android:background="@drawable/badge_background"
                android:paddingHorizontal="12dp"
                android:paddingVertical="4dp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
