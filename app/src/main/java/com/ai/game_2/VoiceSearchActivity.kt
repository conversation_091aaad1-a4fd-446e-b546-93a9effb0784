package com.ai.game_2

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import kotlinx.coroutines.*
import java.util.*
import android.graphics.Bitmap

class VoiceSearchActivity : AppCompatActivity() {
    
    private lateinit var voiceButton: Button
    private lateinit var searchButton: Button
    private lateinit var backButton: Button
    private lateinit var searchInput: EditText
    private lateinit var statusText: TextView
    private lateinit var progressBar: ProgressBar
    private lateinit var imageRecyclerView: RecyclerView
    private lateinit var startGameButton: Button
    
    private var searchQuery: String = ""
    private var downloadedImages: MutableList<String> = mutableListOf()
    private var preloadedImages: MutableList<String> = mutableListOf()
    private lateinit var imageAdapter: DownloadedImageAdapter
    private lateinit var imageSearchService: ImageSearchService
    private var searchJob: Job? = null
    
    companion object {
        private const val REQUEST_RECORD_AUDIO_PERMISSION = 200
        private const val REQUEST_SPEECH_INPUT = 100
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_voice_search)
        
        imageSearchService = ImageSearchService()

        initializeViews()
        setupRecyclerView()
        setupClickListeners()
        checkAudioPermission()
    }
    
    private fun initializeViews() {
        voiceButton = findViewById(R.id.btn_voice_input)
        searchButton = findViewById(R.id.btn_search)
        backButton = findViewById(R.id.btn_back)
        searchInput = findViewById(R.id.et_search_input)
        statusText = findViewById(R.id.tv_status)
        progressBar = findViewById(R.id.progress_bar)
        imageRecyclerView = findViewById(R.id.rv_images)
        startGameButton = findViewById(R.id.btn_start_game)
        
        // 初始状态
        startGameButton.isEnabled = false
        progressBar.visibility = ProgressBar.GONE
    }
    
    private fun setupRecyclerView() {
        imageRecyclerView.layoutManager = GridLayoutManager(this, 2)
        imageAdapter = DownloadedImageAdapter(downloadedImages)
        imageRecyclerView.adapter = imageAdapter
    }
    
    private fun setupClickListeners() {
        voiceButton.setOnClickListener {
            if (checkAudioPermission()) {
                startVoiceInput()
            }
        }
        
        searchButton.setOnClickListener {
            val query = searchInput.text.toString().trim()
            if (query.isNotEmpty()) {
                searchQuery = query
                startImageSearch(query)
            } else {
                Toast.makeText(this, "请输入搜索内容", Toast.LENGTH_SHORT).show()
            }
        }
        
        backButton.setOnClickListener {
            finish()
        }
        
        startGameButton.setOnClickListener {
            if (downloadedImages.size > 0) {
                // 生成关卡列表并跳转到关卡选择界面
                generateLevelsAndNavigate()
            } else {
                Toast.makeText(this, "需要至少1张图片才能生成关卡", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun checkAudioPermission(): Boolean {
        return if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) 
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, 
                arrayOf(Manifest.permission.RECORD_AUDIO), 
                REQUEST_RECORD_AUDIO_PERMISSION)
            false
        } else {
            true
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            REQUEST_RECORD_AUDIO_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(this, "语音权限已获取", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "需要语音权限才能使用语音输入", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
    
    private fun startVoiceInput() {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
            putExtra(RecognizerIntent.EXTRA_PROMPT, "请说出你想要搜索的图片内容...")
        }

        try {
            @Suppress("DEPRECATION")
            startActivityForResult(intent, REQUEST_SPEECH_INPUT)
        } catch (e: Exception) {
            Toast.makeText(this, "语音识别不可用", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == REQUEST_SPEECH_INPUT && resultCode == RESULT_OK) {
            val result = data?.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS)
            result?.let {
                if (it.isNotEmpty()) {
                    val spokenText = it[0]
                    searchInput.setText(spokenText)
                    searchQuery = spokenText
                    statusText.text = "语音识别结果：$spokenText"
                    
                    // 自动开始搜索
                    startImageSearch(spokenText)
                }
            }
        }
    }
    
    private fun startImageSearch(query: String) {
        statusText.text = "正在搜索图片：$query"
        progressBar.visibility = ProgressBar.VISIBLE
        searchButton.isEnabled = false
        voiceButton.isEnabled = false

        // 取消之前的搜索任务
        searchJob?.cancel()

        // 开始真正的网络图片搜索
        searchJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                realImageSearch(query)
            } catch (e: Exception) {
                runOnUiThread {
                    handleSearchError(e)
                }
            }
        }
    }
    
    private suspend fun realImageSearch(query: String) {
        try {
            // 清空之前的图片
            runOnUiThread {
                downloadedImages.clear()
                preloadedImages.clear()
                imageAdapter.notifyDataSetChanged()
            }

            runOnUiThread {
                statusText.text = "正在百度搜索图片：$query"
            }

            println("开始搜索图片: $query") // 调试信息

            // 调用真正的百度图片搜索API
            val imageUrls = imageSearchService.searchImages(query, 10)

            println("搜索结果数量: ${imageUrls.size}") // 调试信息
            imageUrls.forEachIndexed { index, url ->
                println("图片${index + 1}: $url")
            }

            if (imageUrls.isEmpty()) {
                runOnUiThread {
                    statusText.text = "百度搜索失败，请检查网络连接或尝试其他关键词"
                    handleSearchComplete(false)
                }
                return
            }

            runOnUiThread {
                statusText.text = "百度搜索到 ${imageUrls.size} 张相关图片，正在下载..."
            }

            // 逐张加载图片，实时更新预览
            val loadedCount = preloadImages(imageUrls)

            runOnUiThread {
                if (loadedCount > 0) {
                    // 确保downloadedImages包含所有成功加载的图片
                    downloadedImages.clear()
                    downloadedImages.addAll(preloadedImages)
                    statusText.text = "总共成功加载 ${downloadedImages.size} 张图片！"
                    handleSearchComplete(true)
                } else {
                    statusText.text = "所有图片加载失败，请检查网络连接"
                    handleSearchComplete(false)
                }
            }

        } catch (e: Exception) {
            runOnUiThread {
                statusText.text = "搜索失败：${e.message}"
                handleSearchComplete(false)
            }
        }
    }

    private suspend fun preloadImages(imageUrls: List<String>): Int {
        var successCount = 0

        // 逐张加载图片，每张图片必须成功才继续下一张
        for (index in imageUrls.indices) {
            val combinedUrl = imageUrls[index]

            runOnUiThread {
                statusText.text = "正在加载第 ${index + 1} 张图片..."
            }

            try {
                // 尝试多个源直到成功
                val successfulUrl = loadImageWithRetry(combinedUrl, index + 1)

                if (successfulUrl != null) {
                    runOnUiThread {
                        preloadedImages.add(successfulUrl)
                        downloadedImages.add(successfulUrl)
                        // 实时更新预览，每加载成功一张就显示一张
                        imageAdapter.notifyItemInserted(downloadedImages.size - 1)
                        statusText.text = "成功加载第 ${index + 1} 张图片！已加载 ${downloadedImages.size} 张"
                    }
                    successCount++

                    // 加载成功后稍作停顿，让用户看到效果
                    delay(800)
                } else {
                    // 如果所有源都失败，使用默认图片URL
                    val defaultUrl = "default_image_${index + 1}"
                    runOnUiThread {
                        preloadedImages.add(defaultUrl)
                        downloadedImages.add(defaultUrl)
                        imageAdapter.notifyItemInserted(downloadedImages.size - 1)
                        statusText.text = "第 ${index + 1} 张图片使用默认图片，已加载 ${downloadedImages.size} 张"
                    }
                    successCount++
                    delay(500)
                }
            } catch (e: Exception) {
                // 异常情况也使用默认图片
                val defaultUrl = "default_image_${index + 1}"
                runOnUiThread {
                    preloadedImages.add(defaultUrl)
                    downloadedImages.add(defaultUrl)
                    imageAdapter.notifyItemInserted(downloadedImages.size - 1)
                    statusText.text = "第 ${index + 1} 张图片使用默认图片，已加载 ${downloadedImages.size} 张"
                }
                successCount++
                delay(500)
            }
        }

        return successCount
    }

    private suspend fun loadImageWithRetry(combinedUrl: String, imageIndex: Int): String? {
        // 分割获取多个备用源
        val backupSources = combinedUrl.split("|")

        for (sourceIndex in backupSources.indices) {
            val imageUrl = backupSources[sourceIndex]

            runOnUiThread {
                val sourceType = when (sourceIndex) {
                    0 -> "百度搜索结果"
                    1 -> "Unsplash相关"
                    2 -> "备用源1"
                    3 -> "Unsplash自然"
                    4 -> "备用源2"
                    else -> "备用源${sourceIndex}"
                }
                statusText.text = "正在加载第 $imageIndex 张图片... ($sourceType ${sourceIndex + 1}/${backupSources.size})"
            }

            try {
                val success = loadSingleImage(imageUrl)
                if (success) {
                    runOnUiThread {
                        statusText.text = "第 $imageIndex 张图片加载成功！(使用源 ${sourceIndex + 1})"
                    }
                    delay(300) // 短暂停顿显示成功信息
                    return imageUrl // 返回成功的URL
                } else {
                    runOnUiThread {
                        statusText.text = "第 $imageIndex 张图片源 ${sourceIndex + 1} 失败，尝试下一个源..."
                    }
                    delay(200) // 短暂停顿后尝试下一个源
                }
            } catch (e: Exception) {
                runOnUiThread {
                    statusText.text = "第 $imageIndex 张图片源 ${sourceIndex + 1} 出错，尝试下一个源..."
                }
                delay(200)
            }
        }

        // 所有源都失败
        runOnUiThread {
            statusText.text = "第 $imageIndex 张图片所有源都失败，使用默认图片"
        }
        return null
    }

    private suspend fun loadSingleImage(imageUrl: String): Boolean = withContext(Dispatchers.IO) {
        try {
            println("开始加载图片: $imageUrl") // 调试信息

            // 修复Glide配置问题 - 使用asBitmap而不是asFile
            val future = Glide.with(this@VoiceSearchActivity)
                .asBitmap()
                .load(imageUrl)
                .diskCacheStrategy(DiskCacheStrategy.DATA)
                .submit()

            // 等待图片下载完成
            val bitmap = future.get(10, java.util.concurrent.TimeUnit.SECONDS)
            val success = bitmap != null && !bitmap.isRecycled

            if (success) {
                println("图片加载成功: $imageUrl, 尺寸: ${bitmap?.width}x${bitmap?.height}")
            } else {
                println("图片加载失败: $imageUrl, bitmap为空或已回收")
            }

            return@withContext success
        } catch (e: Exception) {
            println("图片加载异常: $imageUrl, 错误: ${e.message}")
            e.printStackTrace()
            return@withContext false
        }
    }

    private fun handleSearchError(e: Exception) {
        progressBar.visibility = ProgressBar.GONE
        statusText.text = "搜索失败：${e.message}"
        searchButton.isEnabled = true
        voiceButton.isEnabled = true
    }

    private fun handleSearchComplete(success: Boolean) {
        progressBar.visibility = ProgressBar.GONE
        startGameButton.isEnabled = success && downloadedImages.size > 0
        if (success && downloadedImages.size > 0) {
            startGameButton.text = "生成 ${downloadedImages.size} 个关卡"
        } else {
            startGameButton.text = "生成关卡"
        }
        searchButton.isEnabled = true
        voiceButton.isEnabled = true
    }
    
    private fun generateLevelsAndNavigate() {
        // 根据搜索到的图片生成关卡列表
        val voiceLevels = mutableListOf<GameLevel>()

        downloadedImages.forEachIndexed { index, imageUrl ->
            val level = GameLevel(
                levelNumber = index + 1,
                gridSize = 3, // 默认3x3网格
                imageResourceId = 0, // 使用网络图片，不需要资源ID
                title = "${searchQuery} - 第${index + 1}关",
                theme = GameTheme.VOICE_SEARCH,
                isUnlocked = index == 0, // 只有第一关解锁
                imageUrl = imageUrl // 添加图片URL
            )
            voiceLevels.add(level)
        }

        // 跳转到关卡选择界面，传递生成的关卡数据
        val intent = Intent(this, LevelSelectActivity::class.java)
        intent.putExtra("theme", GameTheme.VOICE_SEARCH.name)
        intent.putExtra("search_query", searchQuery)
        intent.putStringArrayListExtra("image_urls", ArrayList(downloadedImages))
        intent.putExtra("level_count", voiceLevels.size)
        startActivity(intent)
    }
}
