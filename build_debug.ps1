# Build Debug APK with date naming

Write-Host "Building Princess Puzzle Game Debug APK..." -ForegroundColor Green

# Get current date
$date = Get-Date -Format "yyyyMMdd"
Write-Host "Build date: $date" -ForegroundColor Yellow

# Skip image check for voice search feature build
Write-Host "Skipping image check for voice search feature..." -ForegroundColor Yellow
Write-Host "This build focuses on voice search functionality" -ForegroundColor Green

# Try to find Android Studio's embedded JDK
$androidStudioPaths = @(
    "$env:LOCALAPPDATA\Android\Sdk",
    "$env:PROGRAMFILES\Android\Android Studio",
    "$env:PROGRAMFILES(X86)\Android\Android Studio"
)

$javaPath = $null
foreach ($path in $androidStudioPaths) {
    if (Test-Path $path) {
        $jdkPath = Get-ChildItem -Path $path -Recurse -Directory -Name "*jdk*" | Select-Object -First 1
        if ($jdkPath) {
            $javaExe = Join-Path $path $jdkPath "bin\java.exe"
            if (Test-Path $javaExe) {
                $javaPath = Split-Path $javaExe
                break
            }
        }
    }
}

if ($javaPath) {
    Write-Host "Found Java at: $javaPath" -ForegroundColor Green
    $env:JAVA_HOME = Split-Path $javaPath
    $env:PATH = "$javaPath;$env:PATH"
} else {
    Write-Host "Java not found. Please install Android Studio or set JAVA_HOME" -ForegroundColor Red
    Write-Host "You can also try building in Android Studio directly" -ForegroundColor Yellow
    exit 1
}

# Clean previous build
Write-Host "Cleaning previous build..." -ForegroundColor Yellow
try {
    & .\gradlew.bat clean
    if ($LASTEXITCODE -ne 0) {
        throw "Clean failed"
    }
} catch {
    Write-Host "Clean failed, continuing anyway..." -ForegroundColor Yellow
}

# Build debug APK
Write-Host "Building debug APK..." -ForegroundColor Yellow
try {
    & .\gradlew.bat assembleDebug
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
} catch {
    Write-Host "Build failed!" -ForegroundColor Red
    Write-Host "Please try building in Android Studio:" -ForegroundColor Yellow
    Write-Host "1. Open Android Studio" -ForegroundColor Yellow
    Write-Host "2. Open this project" -ForegroundColor Yellow
    Write-Host "3. Build -> Make Project" -ForegroundColor Yellow
    Write-Host "4. Build -> Build Bundle(s) / APK(s) -> Build APK(s)" -ForegroundColor Yellow
    exit 1
}

# Check if APK was created
$apkPath = "app\build\outputs\apk\debug\app-debug.apk"
if (!(Test-Path $apkPath)) {
    Write-Host "APK not found at expected location: $apkPath" -ForegroundColor Red
    exit 1
}

# Create release directory
if (!(Test-Path "release")) {
    New-Item -ItemType Directory -Path "release" | Out-Null
}

# Copy and rename APK with date
$newApkName = "${date}_PrincessPuzzle_debug.apk"
$outputPath = "release\$newApkName"

Copy-Item $apkPath $outputPath

# Get file size
$fileSize = (Get-Item $outputPath).Length
$fileSizeMB = [math]::Round($fileSize / 1MB, 2)

Write-Host ""
Write-Host "APK Build Successful!" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green
Write-Host "File: $newApkName" -ForegroundColor Cyan
Write-Host "Path: $outputPath" -ForegroundColor Cyan
Write-Host "Size: $fileSizeMB MB" -ForegroundColor Cyan
Write-Host "Date: $date" -ForegroundColor Cyan
Write-Host ""
Write-Host "Installation Instructions:" -ForegroundColor Yellow
Write-Host "1. Transfer APK to Android device" -ForegroundColor White
Write-Host "2. Enable 'Unknown Sources' in device settings" -ForegroundColor White
Write-Host "3. Tap APK file to install" -ForegroundColor White
Write-Host "4. Enjoy the Princess Puzzle Game!" -ForegroundColor White
Write-Host ""

# Ask to open folder
$openFolder = Read-Host "Open APK folder? (y/n)"
if ($openFolder -eq "y" -or $openFolder -eq "Y") {
    Invoke-Item "release"
}

Write-Host "Build complete!" -ForegroundColor Green
