package com.ai.game_2

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity

class MainActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 隐藏状态栏和导航栏，实现全屏
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN or
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )

        setContentView(R.layout.activity_main)

        setupUI()
    }
    
    private fun setupUI() {
        val startGameButton = findViewById<Button>(R.id.btn_start_game)
        val levelSelectButton = findViewById<Button>(R.id.btn_level_select)
        val exitButton = findViewById<Button>(R.id.btn_exit)
        
        startGameButton.setOnClickListener {
            // 开始可爱主题第一关
            startGame(GameTheme.CUTE, 1)
        }
        
        levelSelectButton.setOnClickListener {
            val intent = Intent(this, ThemeSelectActivity::class.java)
            startActivity(intent)
        }
        
        exitButton.setOnClickListener {
            finish()
        }
    }
    
    private fun startGame(theme: GameTheme, level: Int) {
        val intent = Intent(this, PuzzleGameActivity::class.java)
        intent.putExtra("theme", theme.name)
        intent.putExtra("level", level)
        startActivity(intent)
    }
}
