package com.ai.game_2

import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.view.View
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView

class LevelSelectActivity : AppCompatActivity() {
    
    private lateinit var recyclerView: RecyclerView
    private lateinit var backButton: Button
    private lateinit var levelAdapter: LevelAdapter
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var currentTheme: GameTheme
    private var voiceSearchQuery: String? = null
    private var voiceImageUrls: List<String>? = null
    private var photoPaths: List<String>? = null
    private var customGridCols: Int = 3
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_level_select)

        sharedPreferences = getSharedPreferences("PuzzleGame", MODE_PRIVATE)

        // 获取主题参数
        val themeName = intent.getStringExtra("theme") ?: GameTheme.CUTE.name
        currentTheme = GameTheme.valueOf(themeName)

        // 获取语音搜图的额外参数
        if (currentTheme == GameTheme.VOICE_SEARCH) {
            voiceSearchQuery = intent.getStringExtra("search_query")
            voiceImageUrls = intent.getStringArrayListExtra("image_urls")
        }

        // 获取拍照主题的额外参数
        if (currentTheme == GameTheme.CUSTOM_PHOTO) {
            photoPaths = intent.getStringArrayListExtra("photo_paths")
            customGridCols = intent.getIntExtra("custom_grid_cols", 3)
        }

        initializeViews()
        setupRecyclerView()
        setupClickListeners()
    }
    
    override fun onResume() {
        super.onResume()
        // 每次返回时刷新关卡状态
        levelAdapter.updateLevels(getUpdatedLevels())
    }
    
    private fun initializeViews() {
        recyclerView = findViewById(R.id.rv_levels)
        backButton = findViewById(R.id.btn_back)
    }
    
    private fun setupRecyclerView() {
        recyclerView.layoutManager = GridLayoutManager(this, 2)
        levelAdapter = LevelAdapter(getUpdatedLevels()) { level ->
            if (level.isUnlocked) {
                when (level.theme) {
                    GameTheme.CUSTOM_PHOTO -> startPhotoPuzzleGame(level)
                    GameTheme.VOICE_SEARCH -> startVoicePuzzleGame(level)
                    else -> startGame(level.theme, level.levelNumber)
                }
            }
        }
        recyclerView.adapter = levelAdapter
    }
    
    private fun setupClickListeners() {
        backButton.setOnClickListener {
            finish()
        }
    }
    
    private fun getUpdatedLevels(): List<GameLevel> {
        // 如果是语音搜图主题且有动态数据，使用动态生成的关卡
        if (currentTheme == GameTheme.VOICE_SEARCH && voiceImageUrls != null && voiceSearchQuery != null) {
            return generateVoiceLevels()
        }

        // 如果是拍照主题且有照片数据，使用动态生成的关卡
        if (currentTheme == GameTheme.CUSTOM_PHOTO && photoPaths != null) {
            return generatePhotoLevels()
        }

        // 否则使用默认的关卡数据
        val levels = GameLevelManager.getLevelsForTheme(currentTheme).toMutableList()
        val maxUnlockedLevel = sharedPreferences.getInt("max_unlocked_level_${currentTheme.name}", 1)

        // 更新解锁状态
        levels.forEachIndexed { index, level ->
            levels[index] = level.copy(isUnlocked = level.levelNumber <= maxUnlockedLevel)
        }

        return levels
    }

    private fun generateVoiceLevels(): List<GameLevel> {
        val imageUrls = voiceImageUrls ?: return emptyList()
        val query = voiceSearchQuery ?: "搜索结果"

        // 获取语音搜图主题的最大解锁关卡
        val maxUnlockedLevel = sharedPreferences.getInt("max_unlocked_level_${GameTheme.VOICE_SEARCH.name}", 1)

        return imageUrls.mapIndexed { index, imageUrl ->
            GameLevel(
                levelNumber = index + 1,
                gridSize = 3,
                imageResourceId = 0,
                title = "$query - 第${index + 1}关",
                theme = GameTheme.VOICE_SEARCH,
                isUnlocked = (index + 1) <= maxUnlockedLevel, // 按进度解锁
                imageUrl = imageUrl
            )
        }
    }

    private fun generatePhotoLevels(): List<GameLevel> {
        val photos = photoPaths ?: return emptyList()

        // 获取拍照主题的最大解锁关卡
        val maxUnlockedLevel = sharedPreferences.getInt("max_unlocked_level_${GameTheme.CUSTOM_PHOTO.name}", 1)

        return photos.mapIndexed { index, photoPath ->
            GameLevel(
                levelNumber = index + 1,
                gridSize = 3,
                imageResourceId = 0,
                title = "我的照片 - 第${index + 1}关",
                theme = GameTheme.CUSTOM_PHOTO,
                isUnlocked = (index + 1) <= maxUnlockedLevel, // 按进度解锁
                imageUrl = photoPath
            )
        }
    }
    
    private fun startGame(theme: GameTheme, level: Int) {
        // 直接进入游戏，使用全局难度设置
        val intent = Intent(this, PuzzleGameActivity::class.java)
        intent.putExtra("theme", theme.name)
        intent.putExtra("level", level)
        startActivity(intent)
    }

    private fun startCameraActivity(level: Int) {
        val intent = Intent(this, CameraActivity::class.java)
        intent.putExtra("level", level)
        startActivity(intent)
    }

    private fun startVoiceSearchActivity() {
        val intent = Intent(this, VoiceSearchActivity::class.java)
        startActivity(intent)
    }

    private fun startVoicePuzzleGame(level: GameLevel) {
        // 语音搜图主题直接进入游戏，使用全局难度
        val intent = Intent(this, PuzzleGameActivity::class.java)
        intent.putExtra("theme", GameTheme.VOICE_SEARCH.name)
        intent.putExtra("level", level.levelNumber)
        intent.putExtra("search_query", voiceSearchQuery)
        intent.putStringArrayListExtra("image_urls", ArrayList(voiceImageUrls ?: emptyList()))
        intent.putExtra("useDynamicGrid", true)
        startActivity(intent)
    }

    private fun startPhotoPuzzleGame(level: GameLevel) {
        val photos = photoPaths ?: return
        if (level.levelNumber <= photos.size) {
            val photoPath = photos[level.levelNumber - 1]

            // 拍照主题直接进入游戏，使用全局难度
            val intent = Intent(this, PuzzleGameActivity::class.java)
            intent.putExtra("theme", GameTheme.CUSTOM_PHOTO.name)
            intent.putExtra("level", level.levelNumber)
            intent.putExtra("custom_photo_path", photoPath)
            intent.putExtra("custom_grid_cols", customGridCols)
            intent.putExtra("useDynamicGrid", true)
            // 传递所有照片路径，用于下一关导航
            intent.putStringArrayListExtra("photo_paths", ArrayList(photos))
            startActivity(intent)
        }
    }
}
