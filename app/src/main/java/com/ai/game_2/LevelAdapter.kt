package com.ai.game_2

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import kotlinx.coroutines.*

class LevelAdapter(
    private var levels: List<GameLevel>,
    private val onLevelClick: (GameLevel) -> Unit
) : RecyclerView.Adapter<LevelAdapter.LevelViewHolder>() {
    
    class LevelViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val levelNumber: TextView = itemView.findViewById(R.id.tv_level_number)
        val levelTitle: TextView = itemView.findViewById(R.id.tv_level_title)
        val levelImage: ImageView = itemView.findViewById(R.id.iv_level_image)
        val lockIcon: ImageView = itemView.findViewById(R.id.iv_lock_icon)
        val pieceCount: TextView = itemView.findViewById(R.id.tv_piece_count)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LevelViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_level, parent, false)
        return LevelViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: LevelViewHolder, position: Int) {
        val level = levels[position]
        
        holder.levelNumber.text = "第${level.levelNumber}关"
        holder.levelTitle.text = level.title
        // 根据主题类型显示拼图块数量
        val pieceCountText = when (level.theme) {
            GameTheme.CUSTOM_PHOTO -> "智能分配"
            GameTheme.VOICE_SEARCH -> "9块"
            else -> "9块"  // 其他主题固定9块
        }
        holder.pieceCount.text = pieceCountText
        
        if (level.isUnlocked) {
            // 根据主题类型设置图片
            when (level.theme) {
                GameTheme.CUTE -> {
                    val generator = CuteImageGenerator(holder.itemView.context)

                    // 检查关卡是否可用
                    if (!generator.isLevelAvailable(level.levelNumber)) {
                        // 关卡不可用，显示禁用状态
                        holder.levelImage.setImageResource(R.drawable.ic_voice_placeholder)
                        holder.levelImage.alpha = 0.3f // 半透明显示不可用
                        holder.levelTitle.alpha = 0.3f
                        holder.levelNumber.alpha = 0.3f
                        holder.itemView.isEnabled = false
                        return@bind
                    }

                    // 关卡可用，恢复正常状态
                    holder.levelImage.alpha = 1.0f
                    holder.levelTitle.alpha = 1.0f
                    holder.levelNumber.alpha = 1.0f
                    holder.itemView.isEnabled = true

                    // 先显示占位符，然后异步加载可爱图片
                    holder.levelImage.setImageResource(R.drawable.ic_voice_placeholder)

                    // 异步生成图片，避免阻塞主线程
                    CoroutineScope(Dispatchers.Main).launch {
                        try {
                            val cuteBitmap = generator.generateCuteImage(level.levelNumber)
                            if (cuteBitmap != null) {
                                holder.levelImage.setImageBitmap(cuteBitmap)
                            } else {
                                // 下载失败，标记为不可用
                                holder.levelImage.alpha = 0.3f
                                holder.levelText.alpha = 0.3f
                                holder.itemView.isEnabled = false
                            }
                        } catch (e: Exception) {
                            // 如果生成失败，标记为不可用
                            holder.levelImage.alpha = 0.3f
                            holder.levelText.alpha = 0.3f
                            holder.itemView.isEnabled = false
                        }
                    }
                }
                GameTheme.VOICE_SEARCH -> {
                    // 语音搜图主题使用网络图片
                    if (!level.imageUrl.isNullOrEmpty()) {
                        Glide.with(holder.itemView.context)
                            .load(level.imageUrl)
                            .placeholder(R.drawable.ic_voice_placeholder)
                            .error(R.drawable.ic_voice_placeholder)
                            .centerCrop()
                            .into(holder.levelImage)
                    } else {
                        holder.levelImage.setImageResource(R.drawable.ic_voice_placeholder)
                    }
                }
                else -> {
                    // 其他主题使用资源图片
                    holder.levelImage.setImageResource(level.imageResourceId)
                }
            }
            holder.levelImage.alpha = 1.0f
            holder.lockIcon.visibility = View.GONE
            holder.itemView.isEnabled = true
            holder.itemView.alpha = 1.0f
        } else {
            holder.levelImage.setImageResource(R.drawable.ic_lock)
            holder.levelImage.alpha = 0.5f
            holder.lockIcon.visibility = View.VISIBLE
            holder.itemView.isEnabled = false
            holder.itemView.alpha = 0.5f
        }
        
        holder.itemView.setOnClickListener {
            if (level.isUnlocked) {
                onLevelClick(level)
            }
        }
    }
    
    override fun getItemCount(): Int = levels.size
    
    fun updateLevels(newLevels: List<GameLevel>) {
        levels = newLevels
        notifyDataSetChanged()
    }
}
