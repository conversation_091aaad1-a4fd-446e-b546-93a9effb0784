#!/usr/bin/env python3
"""
快速下载公主图片脚本
"""

import requests
import os
from urllib.parse import urlparse

def download_image(url, filename):
    """下载图片到指定文件名"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 确保目录存在
        os.makedirs('app/src/main/res/drawable', exist_ok=True)
        
        # 保存图片
        filepath = f'app/src/main/res/drawable/{filename}'
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ 成功下载: {filename}")
        return True
    except Exception as e:
        print(f"❌ 下载失败 {filename}: {e}")
        return False

def download_sample_images():
    """下载一些示例公主图片"""
    
    # 这些是一些免费的示例图片URL
    # 实际使用时，你需要从Pixabay等网站获取真实的图片URL
    sample_images = [
        {
            'url': 'https://cdn.pixabay.com/photo/2023/01/15/12/34/princess-7720123_1280.png',
            'filename': 'puzzle_image_1.png',
            'description': '艾莎公主'
        },
        {
            'url': 'https://cdn.pixabay.com/photo/2023/02/20/08/45/mermaid-7801234_1280.png',
            'filename': 'puzzle_image_2.png',
            'description': '美人鱼公主'
        },
        {
            'url': 'https://cdn.pixabay.com/photo/2023/03/10/14/22/unicorn-7845123_1280.png',
            'filename': 'puzzle_image_3.png',
            'description': '独角兽'
        },
        {
            'url': 'https://cdn.pixabay.com/photo/2023/04/05/16/33/fairy-7901234_1280.png',
            'filename': 'puzzle_image_4.png',
            'description': '花仙子'
        },
        {
            'url': 'https://cdn.pixabay.com/photo/2023/05/12/09/44/pony-7956123_1280.png',
            'filename': 'puzzle_image_5.png',
            'description': '彩虹小马'
        }
    ]
    
    print("🎨 开始下载公主图片...")
    print("注意：这些是示例URL，实际使用时请从Pixabay手动选择图片！")
    print()
    
    success_count = 0
    for img in sample_images:
        print(f"正在下载 {img['description']}...")
        if download_image(img['url'], img['filename']):
            success_count += 1
    
    print(f"\n✨ 完成！成功下载 {success_count}/{len(sample_images)} 张图片")
    
    if success_count < len(sample_images):
        print("\n💡 手动下载建议：")
        print("1. 访问 https://pixabay.com/illustrations/search/princess/")
        print("2. 搜索 'cute princess cartoon'")
        print("3. 选择免费的PNG图片")
        print("4. 下载并重命名为 puzzle_image_*.png")

def show_manual_download_guide():
    """显示手动下载指南"""
    print("🎯 手动下载真实图片指南")
    print("=" * 50)
    print()
    
    steps = [
        "1. 打开浏览器，访问 https://pixabay.com/",
        "2. 在搜索框输入 'cute princess cartoon'",
        "3. 点击 'Illustrations' 筛选插画",
        "4. 选择你喜欢的公主图片",
        "5. 点击图片进入详情页",
        "6. 点击 'Free Download' 按钮",
        "7. 选择合适的尺寸（推荐1280x1280或更大）",
        "8. 下载完成后重命名为 puzzle_image_1.png",
        "9. 重复步骤2-8，下载其他4张图片",
        "10. 将所有图片放入 app/src/main/res/drawable/ 目录"
    ]
    
    for step in steps:
        print(step)
    
    print()
    print("🎨 推荐搜索关键词：")
    print("• puzzle_image_1.png: 'elsa princess cartoon'")
    print("• puzzle_image_2.png: 'mermaid princess cute'")
    print("• puzzle_image_3.png: 'cute unicorn kawaii'")
    print("• puzzle_image_4.png: 'cute fairy princess'")
    print("• puzzle_image_5.png: 'rainbow pony cute'")
    
    print()
    print("📐 图片要求：")
    print("• 格式：PNG 或 JPG")
    print("• 尺寸：至少 512x512px，推荐 1024x1024px")
    print("• 风格：卡通、可爱、儿童友好")
    print("• 背景：简洁或透明背景")

def main():
    """主函数"""
    print("🎨 公主图片下载工具")
    print("=" * 30)
    print()
    
    print("选择操作:")
    print("1. 显示手动下载指南（推荐）")
    print("2. 尝试下载示例图片（可能失效）")
    print("3. 退出")
    print()
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        show_manual_download_guide()
    elif choice == "2":
        download_sample_images()
    elif choice == "3":
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
